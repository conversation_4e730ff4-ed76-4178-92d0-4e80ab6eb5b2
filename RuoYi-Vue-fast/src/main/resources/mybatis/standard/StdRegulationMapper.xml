<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.standard.mapper.StdRegulationMapper">
    
    <resultMap type="StdRegulation" id="StdRegulationResult">
        <result property="regulationId"    column="regulation_id"    />
        <result property="regulationName"    column="regulation_name"    />
        <result property="attachment"    column="attachment"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectStdRegulationVo">
        select regulation_id, regulation_name, attachment, create_by, create_time, update_by, update_time from std_regulation
    </sql>

    <select id="selectStdRegulationList" parameterType="StdRegulation" resultMap="StdRegulationResult">
        <include refid="selectStdRegulationVo"/>
        <where>  
            <if test="regulationName != null  and regulationName != ''"> and regulation_name like concat('%', #{regulationName}, '%')</if>
        </where>
    </select>
    
    <select id="selectStdRegulationByRegulationId" parameterType="Long" resultMap="StdRegulationResult">
        <include refid="selectStdRegulationVo"/>
        where regulation_id = #{regulationId}
    </select>

    <insert id="insertStdRegulation" parameterType="StdRegulation" useGeneratedKeys="true" keyProperty="regulationId">
        insert into std_regulation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="regulationName != null">regulation_name,</if>
            <if test="attachment != null">attachment,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="regulationName != null">#{regulationName},</if>
            <if test="attachment != null">#{attachment},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateStdRegulation" parameterType="StdRegulation">
        update std_regulation
        <trim prefix="SET" suffixOverrides=",">
            <if test="regulationName != null">regulation_name = #{regulationName},</if>
            <if test="attachment != null">attachment = #{attachment},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where regulation_id = #{regulationId}
    </update>

    <delete id="deleteStdRegulationByRegulationId" parameterType="Long">
        delete from std_regulation where regulation_id = #{regulationId}
    </delete>

    <delete id="deleteStdRegulationByRegulationIds" parameterType="String">
        delete from std_regulation where regulation_id in 
        <foreach item="regulationId" collection="array" open="(" separator="," close=")">
            #{regulationId}
        </foreach>
    </delete>
</mapper>