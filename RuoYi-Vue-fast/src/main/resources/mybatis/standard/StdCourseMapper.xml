<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.standard.mapper.StdCourseMapper">
    
    <resultMap type="StdCourse" id="StdCourseResult">
        <result property="courseId"    column="course_id"    />
        <result property="courseName"    column="course_name"    />
        <result property="standardDesc"    column="standard_desc"    />
        <result property="trainingSpec"    column="training_spec"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectStdCourseVo">
        select course_id, course_name, standard_desc, training_spec, create_by, create_time, update_by, update_time from std_course
    </sql>

    <select id="selectStdCourseList" parameterType="StdCourse" resultMap="StdCourseResult">
        <include refid="selectStdCourseVo"/>
        <where>  
            <if test="courseName != null  and courseName != ''"> and course_name like concat('%', #{courseName}, '%')</if>
        </where>
    </select>
    
    <select id="selectStdCourseByCourseId" parameterType="Long" resultMap="StdCourseResult">
        <include refid="selectStdCourseVo"/>
        where course_id = #{courseId}
    </select>

    <insert id="insertStdCourse" parameterType="StdCourse" useGeneratedKeys="true" keyProperty="courseId">
        insert into std_course
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="courseName != null">course_name,</if>
            <if test="standardDesc != null">standard_desc,</if>
            <if test="trainingSpec != null">training_spec,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="courseName != null">#{courseName},</if>
            <if test="standardDesc != null">#{standardDesc},</if>
            <if test="trainingSpec != null">#{trainingSpec},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateStdCourse" parameterType="StdCourse">
        update std_course
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseName != null">course_name = #{courseName},</if>
            <if test="standardDesc != null">standard_desc = #{standardDesc},</if>
            <if test="trainingSpec != null">training_spec = #{trainingSpec},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where course_id = #{courseId}
    </update>

    <delete id="deleteStdCourseByCourseId" parameterType="Long">
        delete from std_course where course_id = #{courseId}
    </delete>

    <delete id="deleteStdCourseByCourseIds" parameterType="String">
        delete from std_course where course_id in 
        <foreach item="courseId" collection="array" open="(" separator="," close=")">
            #{courseId}
        </foreach>
    </delete>
</mapper>