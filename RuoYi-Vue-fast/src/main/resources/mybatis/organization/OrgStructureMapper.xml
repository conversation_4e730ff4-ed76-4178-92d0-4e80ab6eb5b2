<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.organization.mapper.OrgStructureMapper">
    
    <resultMap type="OrgStructure" id="OrgStructureResult">
        <result property="structureId"    column="structure_id"    />
        <result property="structureName"    column="structure_name"    />
        <result property="parentId"    column="parent_id"    />
        <result property="structureType"    column="structure_type"    />
        <result property="structureAbbreviation"    column="structure_abbreviation"    />
        <result property="director"    column="director"    />
        <result property="contactInfo"    column="contact_info"    />
        <result property="description"    column="description"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOrgStructureVo">
        select structure_id, structure_name, parent_id, structure_type, structure_abbreviation, director, contact_info, description, create_by, create_time, update_by, update_time from org_structure
    </sql>

    <select id="selectOrgStructureList" parameterType="OrgStructure" resultMap="OrgStructureResult">
        <include refid="selectOrgStructureVo"/>
        <where>  
            <if test="structureName != null  and structureName != ''"> and structure_name like concat('%', #{structureName}, '%')</if>
            <if test="structureType != null  and structureType != ''"> and structure_type = #{structureType}</if>
        </where>
    </select>
    
    <select id="selectOrgStructureByStructureId" parameterType="Long" resultMap="OrgStructureResult">
        <include refid="selectOrgStructureVo"/>
        where structure_id = #{structureId}
    </select>

    <insert id="insertOrgStructure" parameterType="OrgStructure" useGeneratedKeys="true" keyProperty="structureId">
        insert into org_structure
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="structureName != null">structure_name,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="structureType != null">structure_type,</if>
            <if test="structureAbbreviation != null">structure_abbreviation,</if>
            <if test="director != null">director,</if>
            <if test="contactInfo != null">contact_info,</if>
            <if test="description != null">description,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="structureName != null">#{structureName},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="structureType != null">#{structureType},</if>
            <if test="structureAbbreviation != null">#{structureAbbreviation},</if>
            <if test="director != null">#{director},</if>
            <if test="contactInfo != null">#{contactInfo},</if>
            <if test="description != null">#{description},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOrgStructure" parameterType="OrgStructure">
        update org_structure
        <trim prefix="SET" suffixOverrides=",">
            <if test="structureName != null">structure_name = #{structureName},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="structureType != null">structure_type = #{structureType},</if>
            <if test="structureAbbreviation != null">structure_abbreviation = #{structureAbbreviation},</if>
            <if test="director != null">director = #{director},</if>
            <if test="contactInfo != null">contact_info = #{contactInfo},</if>
            <if test="description != null">description = #{description},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where structure_id = #{structureId}
    </update>

    <delete id="deleteOrgStructureByStructureId" parameterType="Long">
        delete from org_structure where structure_id = #{structureId}
    </delete>

    <delete id="deleteOrgStructureByStructureIds" parameterType="String">
        delete from org_structure where structure_id in 
        <foreach item="structureId" collection="array" open="(" separator="," close=")">
            #{structureId}
        </foreach>
    </delete>
</mapper>