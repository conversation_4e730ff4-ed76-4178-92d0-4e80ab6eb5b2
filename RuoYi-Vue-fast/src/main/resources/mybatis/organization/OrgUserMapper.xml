<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.organization.mapper.OrgUserMapper">
    
    <resultMap type="OrgUser" id="OrgUserResult">
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="orgName"    column="org_name"    />
        <result property="position"    column="position"    />
        <result property="contactInfo"    column="contact_info"    />
        <result property="gender"    column="gender"    />
        <result property="workStatus"    column="work_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOrgUserVo">
        select user_id, user_name, org_name, position, contact_info, gender, work_status, create_by, create_time, update_by, update_time from org_user
    </sql>

    <select id="selectOrgUserList" parameterType="OrgUser" resultMap="OrgUserResult">
        <include refid="selectOrgUserVo"/>
        <where>  
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
        </where>
    </select>
    
    <select id="selectOrgUserByUserId" parameterType="Long" resultMap="OrgUserResult">
        <include refid="selectOrgUserVo"/>
        where user_id = #{userId}
    </select>

    <insert id="insertOrgUser" parameterType="OrgUser" useGeneratedKeys="true" keyProperty="userId">
        insert into org_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null">user_name,</if>
            <if test="orgName != null">org_name,</if>
            <if test="position != null">position,</if>
            <if test="contactInfo != null">contact_info,</if>
            <if test="gender != null">gender,</if>
            <if test="workStatus != null">work_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null">#{userName},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="position != null">#{position},</if>
            <if test="contactInfo != null">#{contactInfo},</if>
            <if test="gender != null">#{gender},</if>
            <if test="workStatus != null">#{workStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOrgUser" parameterType="OrgUser">
        update org_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="userName != null">user_name = #{userName},</if>
            <if test="orgName != null">org_name = #{orgName},</if>
            <if test="position != null">position = #{position},</if>
            <if test="contactInfo != null">contact_info = #{contactInfo},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="workStatus != null">work_status = #{workStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where user_id = #{userId}
    </update>

    <delete id="deleteOrgUserByUserId" parameterType="Long">
        delete from org_user where user_id = #{userId}
    </delete>

    <delete id="deleteOrgUserByUserIds" parameterType="String">
        delete from org_user where user_id in 
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>
    <delete id="deleteAllOrgUser">
        delete from org_user
    </delete>
</mapper>