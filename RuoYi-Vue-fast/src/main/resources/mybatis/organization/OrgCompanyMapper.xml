<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.organization.mapper.OrgCompanyMapper">
    
    <resultMap type="OrgCompany" id="OrgCompanyResult">
        <result property="companyId"    column="company_id"    />
        <result property="companyName"    column="company_name"    />
        <result property="socialCode"    column="social_code"    />
        <result property="legalPerson"    column="legal_person"    />
        <result property="address"    column="address"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactInfo"    column="contact_info"    />
        <result property="jobCount"    column="job_count"    />
        <result property="signedCount"    column="signed_count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOrgCompanyVo">
        select company_id, company_name, social_code, legal_person, address, contact_person, contact_info, job_count, signed_count, create_by, create_time, update_by, update_time from org_company
    </sql>

    <select id="selectOrgCompanyList" parameterType="OrgCompany" resultMap="OrgCompanyResult">
        <include refid="selectOrgCompanyVo"/>
        <where>  
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
        </where>
    </select>
    
    <select id="selectOrgCompanyByCompanyId" parameterType="Long" resultMap="OrgCompanyResult">
        <include refid="selectOrgCompanyVo"/>
        where company_id = #{companyId}
    </select>

    <insert id="insertOrgCompany" parameterType="OrgCompany" useGeneratedKeys="true" keyProperty="companyId">
        insert into org_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyName != null">company_name,</if>
            <if test="socialCode != null">social_code,</if>
            <if test="legalPerson != null">legal_person,</if>
            <if test="address != null">address,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactInfo != null">contact_info,</if>
            <if test="jobCount != null">job_count,</if>
            <if test="signedCount != null">signed_count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyName != null">#{companyName},</if>
            <if test="socialCode != null">#{socialCode},</if>
            <if test="legalPerson != null">#{legalPerson},</if>
            <if test="address != null">#{address},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactInfo != null">#{contactInfo},</if>
            <if test="jobCount != null">#{jobCount},</if>
            <if test="signedCount != null">#{signedCount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOrgCompany" parameterType="OrgCompany">
        update org_company
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="socialCode != null">social_code = #{socialCode},</if>
            <if test="legalPerson != null">legal_person = #{legalPerson},</if>
            <if test="address != null">address = #{address},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactInfo != null">contact_info = #{contactInfo},</if>
            <if test="jobCount != null">job_count = #{jobCount},</if>
            <if test="signedCount != null">signed_count = #{signedCount},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where company_id = #{companyId}
    </update>

    <delete id="deleteOrgCompanyByCompanyId" parameterType="Long">
        delete from org_company where company_id = #{companyId}
    </delete>

    <delete id="deleteOrgCompanyByCompanyIds" parameterType="String">
        delete from org_company where company_id in 
        <foreach item="companyId" collection="array" open="(" separator="," close=")">
            #{companyId}
        </foreach>
    </delete>

    <delete id="deleteAllOrgCompany">
        delete from org_company
    </delete>
</mapper>