<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.organization.mapper.OrgTeacherMapper">
    
    <resultMap type="OrgTeacher" id="OrgTeacherResult">
        <result property="teacherId"    column="teacher_id"    />
        <result property="teacherName"    column="teacher_name"    />
        <result property="teacherType"    column="teacher_type"    />
        <result property="orgName"    column="org_name"    />
        <result property="position"    column="position"    />
        <result property="gender"    column="gender"    />
        <result property="contactInfo"    column="contact_info"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOrgTeacherVo">
        select teacher_id, teacher_name, teacher_type, org_name, position, gender, contact_info, create_by, create_time, update_by, update_time from org_teacher
    </sql>

    <select id="selectOrgTeacherList" parameterType="OrgTeacher" resultMap="OrgTeacherResult">
        <include refid="selectOrgTeacherVo"/>
        <where>  
            <if test="teacherName != null  and teacherName != ''"> and teacher_name like concat('%', #{teacherName}, '%')</if>
            <if test="teacherType != null  and teacherType != ''"> and teacher_type = #{teacherType}</if>
            <if test="orgName != null  and orgName != ''"> and org_name like concat('%', #{orgName}, '%')</if>
            <if test="position != null  and position != ''"> and position = #{position}</if>
            <if test="gender != null  and gender != ''"> and gender = #{gender}</if>
        </where>
    </select>
    
    <select id="selectOrgTeacherByTeacherId" parameterType="Long" resultMap="OrgTeacherResult">
        <include refid="selectOrgTeacherVo"/>
        where teacher_id = #{teacherId}
    </select>

    <insert id="insertOrgTeacher" parameterType="OrgTeacher" useGeneratedKeys="true" keyProperty="teacherId">
        insert into org_teacher
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherName != null">teacher_name,</if>
            <if test="teacherType != null">teacher_type,</if>
            <if test="orgName != null">org_name,</if>
            <if test="position != null">position,</if>
            <if test="gender != null">gender,</if>
            <if test="contactInfo != null">contact_info,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherName != null">#{teacherName},</if>
            <if test="teacherType != null">#{teacherType},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="position != null">#{position},</if>
            <if test="gender != null">#{gender},</if>
            <if test="contactInfo != null">#{contactInfo},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOrgTeacher" parameterType="OrgTeacher">
        update org_teacher
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherName != null">teacher_name = #{teacherName},</if>
            <if test="teacherType != null">teacher_type = #{teacherType},</if>
            <if test="orgName != null">org_name = #{orgName},</if>
            <if test="position != null">position = #{position},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="contactInfo != null">contact_info = #{contactInfo},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where teacher_id = #{teacherId}
    </update>

    <delete id="deleteOrgTeacherByTeacherId" parameterType="Long">
        delete from org_teacher where teacher_id = #{teacherId}
    </delete>

    <delete id="deleteOrgTeacherByTeacherIds" parameterType="String">
        delete from org_teacher where teacher_id in 
        <foreach item="teacherId" collection="array" open="(" separator="," close=")">
            #{teacherId}
        </foreach>
    </delete>

    <delete id="deleteAllOrgTeacher">
        delete from org_teacher
    </delete>
</mapper>