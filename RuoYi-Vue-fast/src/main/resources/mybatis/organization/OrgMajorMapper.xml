<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.organization.mapper.OrgMajorMapper">
    
    <resultMap type="OrgMajor" id="OrgMajorResult">
        <result property="majorId"    column="major_id"    />
        <result property="majorName"    column="major_name"    />
        <result property="majorDesc"    column="major_desc"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOrgMajorVo">
        select major_id, major_name, major_desc, create_by, create_time, update_by, update_time from org_major
    </sql>

    <select id="selectOrgMajorList" parameterType="OrgMajor" resultMap="OrgMajorResult">
        <include refid="selectOrgMajorVo"/>
        <where>  
            <if test="majorName != null  and majorName != ''"> and major_name like concat('%', #{majorName}, '%')</if>
        </where>
    </select>
    
    <select id="selectOrgMajorByMajorId" parameterType="Long" resultMap="OrgMajorResult">
        <include refid="selectOrgMajorVo"/>
        where major_id = #{majorId}
    </select>

    <insert id="insertOrgMajor" parameterType="OrgMajor" useGeneratedKeys="true" keyProperty="majorId">
        insert into org_major
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="majorName != null">major_name,</if>
            <if test="majorDesc != null">major_desc,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="majorName != null">#{majorName},</if>
            <if test="majorDesc != null">#{majorDesc},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOrgMajor" parameterType="OrgMajor">
        update org_major
        <trim prefix="SET" suffixOverrides=",">
            <if test="majorName != null">major_name = #{majorName},</if>
            <if test="majorDesc != null">major_desc = #{majorDesc},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where major_id = #{majorId}
    </update>

    <delete id="deleteOrgMajorByMajorId" parameterType="Long">
        delete from org_major where major_id = #{majorId}
    </delete>

    <delete id="deleteOrgMajorByMajorIds" parameterType="String">
        delete from org_major where major_id in 
        <foreach item="majorId" collection="array" open="(" separator="," close=")">
            #{majorId}
        </foreach>
    </delete>

    <delete id="deleteAllOrgMajor">
        delete from org_major
    </delete>
</mapper>