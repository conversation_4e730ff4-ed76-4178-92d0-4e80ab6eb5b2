<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.student.mapper.StudentInfoMapper">
    
    <resultMap type="StudentInfo" id="StudentInfoResult">
        <result property="studentId"       column="student_id"      />
        <result property="studentName"     column="student_name"    />
        <result property="studentNumber"   column="student_number"  />
        <result property="className"       column="class_name"      />
        <result property="gender"          column="gender"          />
        <result property="studentStatus"   column="student_status"  />
        <result property="schoolTeacher"   column="school_teacher"  />
        <result property="companyTeacher"  column="company_teacher" />
        <result property="major"           column="major"           />
        <result property="targetPosition"  column="target_position" />
        <result property="residentCompany" column="resident_company"/>
        <result property="salary"          column="salary"          />
        <result property="contractId"      column="contract_id"     />
        <result property="positionCompetencyThreshold" column="position_competency_threshold" />
        <result property="contractName"    column="contract_name"   />
        <result property="contractNumber"  column="contract_number" />
        <result property="contractCompanyName" column="contract_company_name" />
        <result property="contractSignDate" column="contract_sign_date" />
        <result property="postName"        column="post_name"       />
        <result property="contractSalary"  column="contract_salary" />
        <result property="createBy"        column="create_by"       />
        <result property="createTime"      column="create_time"     />
        <result property="updateBy"        column="update_by"       />
        <result property="updateTime"      column="update_time"     />
        <result property="remark"          column="remark"          />
    </resultMap>

    <sql id="selectStudentInfoVo">
        select si.student_id, si.student_name, si.student_number, si.class_name, si.gender, si.student_status,
               si.school_teacher, si.company_teacher, si.major, si.target_position, si.resident_company,
               si.salary, si.contract_id, si.position_competency_threshold, si.create_by, si.create_time, si.update_by, si.update_time, si.remark,
               sc.contract_name, sc.contract_number, sc.company_name as contract_company_name, sc.sign_date as contract_sign_date,
               CASE 
                   WHEN si.target_position IS NOT NULL AND si.target_position != '' THEN si.target_position
                   ELSE sc.post_name
               END as post_name,
               sc.salary as contract_salary
        from student_info si
        left join student_contract sc on si.contract_id = sc.contract_id
    </sql>

    <select id="selectStudentInfoList" parameterType="StudentInfo" resultMap="StudentInfoResult">
        <include refid="selectStudentInfoVo"/>
        <where>  
            <if test="studentName != null and studentName != ''"> and si.student_name like concat('%', #{studentName}, '%')</if>
            <if test="studentNumber != null and studentNumber != ''"> and si.student_number like concat('%', #{studentNumber}, '%')</if>
            <if test="className != null and className != ''"> and si.class_name like concat('%', #{className}, '%')</if>
            <if test="gender != null and gender != ''"> and si.gender = #{gender}</if>
            <if test="studentStatus != null and studentStatus != ''"> and si.student_status = #{studentStatus}</if>
            <if test="schoolTeacher != null and schoolTeacher != ''"> and si.school_teacher like concat('%', #{schoolTeacher}, '%')</if>
            <if test="companyTeacher != null and companyTeacher != ''"> and si.company_teacher like concat('%', #{companyTeacher}, '%')</if>
            <if test="major != null and major != ''"> and si.major like concat('%', #{major}, '%')</if>
            <if test="targetPosition != null and targetPosition != ''"> and si.target_position like concat('%', #{targetPosition}, '%')</if>
            <if test="residentCompany != null and residentCompany != ''"> and si.resident_company like concat('%', #{residentCompany}, '%')</if>
        </where>
        order by si.student_id asc
    </select>
    
    <select id="selectStudentInfoByStudentId" parameterType="Long" resultMap="StudentInfoResult">
        <include refid="selectStudentInfoVo"/>
        where si.student_id = #{studentId}
    </select>

    <select id="checkStudentNumberUnique" parameterType="String" resultMap="StudentInfoResult">
        <include refid="selectStudentInfoVo"/>
        where si.student_number = #{studentNumber} limit 1
    </select>
        
    <insert id="insertStudentInfo" parameterType="StudentInfo" useGeneratedKeys="true" keyProperty="studentId">
        insert into student_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="studentName != null and studentName != ''">student_name,</if>
            <if test="studentNumber != null and studentNumber != ''">student_number,</if>
            <if test="className != null">class_name,</if>
            <if test="gender != null">gender,</if>
            <if test="studentStatus != null">student_status,</if>
            <if test="schoolTeacher != null">school_teacher,</if>
            <if test="companyTeacher != null">company_teacher,</if>
            <if test="major != null">major,</if>
            <if test="targetPosition != null">target_position,</if>
            <if test="residentCompany != null">resident_company,</if>
            <if test="salary != null">salary,</if>
            <if test="contractId != null">contract_id,</if>
            <if test="positionCompetencyThreshold != null">position_competency_threshold,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="studentName != null and studentName != ''">#{studentName},</if>
            <if test="studentNumber != null and studentNumber != ''">#{studentNumber},</if>
            <if test="className != null">#{className},</if>
            <if test="gender != null">#{gender},</if>
            <if test="studentStatus != null">#{studentStatus},</if>
            <if test="schoolTeacher != null">#{schoolTeacher},</if>
            <if test="companyTeacher != null">#{companyTeacher},</if>
            <if test="major != null">#{major},</if>
            <if test="targetPosition != null">#{targetPosition},</if>
            <if test="residentCompany != null">#{residentCompany},</if>
            <if test="salary != null">#{salary},</if>
            <if test="contractId != null">#{contractId},</if>
            <if test="positionCompetencyThreshold != null">#{positionCompetencyThreshold},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateStudentInfo" parameterType="StudentInfo">
        update student_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="studentName != null and studentName != ''">student_name = #{studentName},</if>
            <if test="studentNumber != null and studentNumber != ''">student_number = #{studentNumber},</if>
            <if test="className != null">class_name = #{className},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="studentStatus != null">student_status = #{studentStatus},</if>
            <if test="schoolTeacher != null">school_teacher = #{schoolTeacher},</if>
            <if test="companyTeacher != null">company_teacher = #{companyTeacher},</if>
            <if test="major != null">major = #{major},</if>
            <if test="targetPosition != null">target_position = #{targetPosition},</if>
            <if test="residentCompany != null">resident_company = #{residentCompany},</if>
            <if test="salary != null">salary = #{salary},</if>
            <if test="contractId != null">contract_id = #{contractId},</if>
            <if test="positionCompetencyThreshold != null">position_competency_threshold = #{positionCompetencyThreshold},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where student_id = #{studentId}
    </update>

    <delete id="deleteStudentInfoByStudentId" parameterType="Long">
        delete from student_info where student_id = #{studentId}
    </delete>

    <delete id="deleteStudentInfoByStudentIds" parameterType="String">
        delete from student_info where student_id in
        <foreach item="studentId" collection="array" open="(" separator="," close=")">
            #{studentId}
        </foreach>
    </delete>


</mapper> 