<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.course.mapper.CourseCompanyMapper">
    
    <resultMap type="CourseCompany" id="CourseCompanyResult">
        <result property="courseId"    column="course_id"    />
        <result property="courseName"    column="course_name"    />
        <result property="courseStandard"    column="course_standard"    />
        <result property="courseHours"    column="course_hours"    />
        <result property="courseCredit"    column="course_credit"    />
        <result property="courseGoal"    column="course_goal"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCourseCompanyVo">
        select course_id, course_name, course_standard, course_hours, course_credit, course_goal, create_by, create_time, update_by, update_time from course_company
    </sql>

    <select id="selectCourseCompanyList" parameterType="CourseCompany" resultMap="CourseCompanyResult">
        <include refid="selectCourseCompanyVo"/>
        <where>  
            <if test="courseName != null  and courseName != ''"> and course_name like concat('%', #{courseName}, '%')</if>
        </where>
    </select>
    
    <select id="selectCourseCompanyByCourseId" parameterType="Long" resultMap="CourseCompanyResult">
        <include refid="selectCourseCompanyVo"/>
        where course_id = #{courseId}
    </select>

    <insert id="insertCourseCompany" parameterType="CourseCompany" useGeneratedKeys="true" keyProperty="courseId">
        insert into course_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="courseName != null">course_name,</if>
            <if test="courseStandard != null">course_standard,</if>
            <if test="courseHours != null">course_hours,</if>
            <if test="courseCredit != null">course_credit,</if>
            <if test="courseGoal != null">course_goal,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="courseName != null">#{courseName},</if>
            <if test="courseStandard != null">#{courseStandard},</if>
            <if test="courseHours != null">#{courseHours},</if>
            <if test="courseCredit != null">#{courseCredit},</if>
            <if test="courseGoal != null">#{courseGoal},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCourseCompany" parameterType="CourseCompany">
        update course_company
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseName != null">course_name = #{courseName},</if>
            <if test="courseStandard != null">course_standard = #{courseStandard},</if>
            <if test="courseHours != null">course_hours = #{courseHours},</if>
            <if test="courseCredit != null">course_credit = #{courseCredit},</if>
            <if test="courseGoal != null">course_goal = #{courseGoal},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where course_id = #{courseId}
    </update>

    <delete id="deleteCourseCompanyByCourseId" parameterType="Long">
        delete from course_company where course_id = #{courseId}
    </delete>

    <delete id="deleteCourseCompanyByCourseIds" parameterType="String">
        delete from course_company where course_id in 
        <foreach item="courseId" collection="array" open="(" separator="," close=")">
            #{courseId}
        </foreach>
    </delete>
</mapper>