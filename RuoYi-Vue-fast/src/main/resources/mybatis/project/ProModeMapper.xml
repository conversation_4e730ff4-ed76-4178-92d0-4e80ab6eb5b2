<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.project.mapper.ProModeMapper">
    
    <resultMap type="ProMode" id="ProModeResult">
        <result property="modeId"    column="mode_id"    />
        <result property="modeName"    column="mode_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProModeVo">
        select mode_id, mode_name, create_by, create_time, update_by, update_time from pro_mode
    </sql>

    <select id="selectProModeList" parameterType="ProMode" resultMap="ProModeResult">
        <include refid="selectProModeVo"/>
        <where>  
            <if test="modeName != null  and modeName != ''"> and mode_name like concat('%', #{modeName}, '%')</if>
        </where>
    </select>
    
    <select id="selectProModeByModeId" parameterType="Long" resultMap="ProModeResult">
        <include refid="selectProModeVo"/>
        where mode_id = #{modeId}
    </select>

    <insert id="insertProMode" parameterType="ProMode" useGeneratedKeys="true" keyProperty="modeId">
        insert into pro_mode
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="modeName != null">mode_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="modeName != null">#{modeName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProMode" parameterType="ProMode">
        update pro_mode
        <trim prefix="SET" suffixOverrides=",">
            <if test="modeName != null">mode_name = #{modeName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where mode_id = #{modeId}
    </update>

    <delete id="deleteProModeByModeId" parameterType="Long">
        delete from pro_mode where mode_id = #{modeId}
    </delete>

    <delete id="deleteProModeByModeIds" parameterType="String">
        delete from pro_mode where mode_id in 
        <foreach item="modeId" collection="array" open="(" separator="," close=")">
            #{modeId}
        </foreach>
    </delete>

    <delete id="deleteAllProMode">
        delete from pro_mode
    </delete>
</mapper>