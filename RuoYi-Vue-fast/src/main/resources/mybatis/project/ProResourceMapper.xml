<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.project.mapper.ProResourceMapper">
    
    <resultMap type="ProResource" id="ProResourceResult">
        <result property="resourceId"    column="resource_id"    />
        <result property="resourceName"    column="resource_name"    />
        <result property="resourceCategory"    column="resource_category"    />
        <result property="resourceAttachment"    column="resource_attachment"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProResourceVo">
        select resource_id, resource_name, resource_category, resource_attachment, create_by, create_time, update_by, update_time from pro_resource
    </sql>

    <select id="selectProResourceList" parameterType="ProResource" resultMap="ProResourceResult">
        <include refid="selectProResourceVo"/>
        <where>  
            <if test="resourceName != null  and resourceName != ''"> and resource_name like concat('%', #{resourceName}, '%')</if>
            <if test="resourceCategory != null  and resourceCategory != ''"> and resource_category = #{resourceCategory}</if>
        </where>
    </select>
    
    <select id="selectProResourceByResourceId" parameterType="Long" resultMap="ProResourceResult">
        <include refid="selectProResourceVo"/>
        where resource_id = #{resourceId}
    </select>

    <insert id="insertProResource" parameterType="ProResource" useGeneratedKeys="true" keyProperty="resourceId">
        insert into pro_resource
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="resourceName != null">resource_name,</if>
            <if test="resourceCategory != null">resource_category,</if>
            <if test="resourceAttachment != null">resource_attachment,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="resourceName != null">#{resourceName},</if>
            <if test="resourceCategory != null">#{resourceCategory},</if>
            <if test="resourceAttachment != null">#{resourceAttachment},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProResource" parameterType="ProResource">
        update pro_resource
        <trim prefix="SET" suffixOverrides=",">
            <if test="resourceName != null">resource_name = #{resourceName},</if>
            <if test="resourceCategory != null">resource_category = #{resourceCategory},</if>
            <if test="resourceAttachment != null">resource_attachment = #{resourceAttachment},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where resource_id = #{resourceId}
    </update>

    <delete id="deleteProResourceByResourceId" parameterType="Long">
        delete from pro_resource where resource_id = #{resourceId}
    </delete>

    <delete id="deleteProResourceByResourceIds" parameterType="String">
        delete from pro_resource where resource_id in 
        <foreach item="resourceId" collection="array" open="(" separator="," close=")">
            #{resourceId}
        </foreach>
    </delete>
</mapper>