<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.project.mapper.ProTrainingMapper">
    
    <resultMap type="ProTraining" id="ProTrainingResult">
        <result property="projectId"    column="project_id"    />
        <result property="projectName"    column="project_name"    />
        <result property="trainingMode"    column="training_mode"    />
        <result property="trainingDays"    column="training_days"    />
        <result property="projectStatus"    column="project_status"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="projectDesc"    column="project_desc"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProTrainingVo">
        select project_id, project_name, training_mode, training_days, project_status, start_date, end_date, project_desc, create_by, create_time, update_by, update_time from pro_training
    </sql>

    <select id="selectProTrainingList" parameterType="ProTraining" resultMap="ProTrainingResult">
        <include refid="selectProTrainingVo"/>
        <where>  
            <if test="projectName != null  and projectName != ''"> and project_name like concat('%', #{projectName}, '%')</if>
            <if test="trainingMode != null  and trainingMode != ''"> and training_mode = #{trainingMode}</if>
        </where>
    </select>
    
    <select id="selectProTrainingByProjectId" parameterType="Long" resultMap="ProTrainingResult">
        <include refid="selectProTrainingVo"/>
        where project_id = #{projectId}
    </select>

    <insert id="insertProTraining" parameterType="ProTraining" useGeneratedKeys="true" keyProperty="projectId">
        insert into pro_training
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectName != null">project_name,</if>
            <if test="trainingMode != null">training_mode,</if>
            <if test="trainingDays != null">training_days,</if>
            <if test="projectStatus != null">project_status,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="projectDesc != null">project_desc,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectName != null">#{projectName},</if>
            <if test="trainingMode != null">#{trainingMode},</if>
            <if test="trainingDays != null">#{trainingDays},</if>
            <if test="projectStatus != null">#{projectStatus},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="projectDesc != null">#{projectDesc},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProTraining" parameterType="ProTraining">
        update pro_training
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectName != null">project_name = #{projectName},</if>
            <if test="trainingMode != null">training_mode = #{trainingMode},</if>
            <if test="trainingDays != null">training_days = #{trainingDays},</if>
            <if test="projectStatus != null">project_status = #{projectStatus},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="projectDesc != null">project_desc = #{projectDesc},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where project_id = #{projectId}
    </update>

    <delete id="deleteProTrainingByProjectId" parameterType="Long">
        delete from pro_training where project_id = #{projectId}
    </delete>

    <delete id="deleteProTrainingByProjectIds" parameterType="String">
        delete from pro_training where project_id in 
        <foreach item="projectId" collection="array" open="(" separator="," close=")">
            #{projectId}
        </foreach>
    </delete>

    <delete id="deleteAllProTraining">
        delete from pro_training
    </delete>
</mapper>