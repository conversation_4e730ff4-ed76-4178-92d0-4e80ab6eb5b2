<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.training.mapper.EvaluationRecordAnswerMapper">
    
    <resultMap type="EvaluationRecordAnswer" id="EvaluationRecordAnswerResult">
        <result property="answerId"    column="answer_id"    />
        <result property="recordId"    column="record_id"    />
        <result property="questionId"    column="question_id"    />
        <result property="questionStem"    column="question_stem"    />
        <result property="questionType"    column="question_type"    />
        <result property="options"    column="options"    />
        <result property="userAnswer"    column="user_answer"    />
        <result property="answerTime"    column="answer_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectEvaluationRecordAnswerVo">
        select answer_id, record_id, question_id, question_stem, question_type, options, user_answer, answer_time, create_by, create_time, update_by, update_time
        from evaluation_record_answer
    </sql>

    <select id="selectEvaluationRecordAnswerList" parameterType="EvaluationRecordAnswer" resultMap="EvaluationRecordAnswerResult">
        <include refid="selectEvaluationRecordAnswerVo"/>
        <where>  
            <if test="recordId != null "> and record_id = #{recordId}</if>
            <if test="questionId != null "> and question_id = #{questionId}</if>
            <if test="questionStem != null  and questionStem != ''"> and question_stem like concat('%', #{questionStem}, '%')</if>
            <if test="questionType != null  and questionType != ''"> and question_type = #{questionType}</if>
            <if test="userAnswer != null  and userAnswer != ''"> and user_answer like concat('%', #{userAnswer}, '%')</if>
            <if test="answerTime != null "> and answer_time = #{answerTime}</if>
        </where>
        order by answer_time desc
    </select>
    
    <select id="selectEvaluationRecordAnswerByAnswerId" parameterType="Long" resultMap="EvaluationRecordAnswerResult">
        <include refid="selectEvaluationRecordAnswerVo"/>
        where answer_id = #{answerId}
    </select>

    <select id="selectEvaluationRecordAnswerListByRecordId" parameterType="Long" resultMap="EvaluationRecordAnswerResult">
        <include refid="selectEvaluationRecordAnswerVo"/>
        where record_id = #{recordId}
        order by answer_time asc
    </select>
        
    <insert id="insertEvaluationRecordAnswer" parameterType="EvaluationRecordAnswer" useGeneratedKeys="true" keyProperty="answerId">
        insert into evaluation_record_answer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recordId != null">record_id,</if>
            <if test="questionId != null">question_id,</if>
            <if test="questionStem != null and questionStem != ''">question_stem,</if>
            <if test="questionType != null and questionType != ''">question_type,</if>
            <if test="options != null and options != ''">options,</if>
            <if test="userAnswer != null">user_answer,</if>
            <if test="answerTime != null">answer_time,</if>
            <if test="createBy != null">create_by,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recordId != null">#{recordId},</if>
            <if test="questionId != null">#{questionId},</if>
            <if test="questionStem != null and questionStem != ''">#{questionStem},</if>
            <if test="questionType != null and questionType != ''">#{questionType},</if>
            <if test="options != null and options != ''">#{options},</if>
            <if test="userAnswer != null">#{userAnswer},</if>
            <if test="answerTime != null">#{answerTime},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
         </trim>
    </insert>

    <insert id="batchInsertEvaluationRecordAnswer" parameterType="java.util.List">
        insert into evaluation_record_answer (record_id, question_id, question_stem, question_type, options, user_answer, answer_time, create_by, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.recordId}, #{item.questionId}, #{item.questionStem}, #{item.questionType}, #{item.options}, #{item.userAnswer}, #{item.answerTime}, #{item.createBy}, sysdate())
        </foreach>
    </insert>

    <update id="updateEvaluationRecordAnswer" parameterType="EvaluationRecordAnswer">
        update evaluation_record_answer
        <trim prefix="SET" suffixOverrides=",">
            <if test="recordId != null">record_id = #{recordId},</if>
            <if test="questionId != null">question_id = #{questionId},</if>
            <if test="questionStem != null and questionStem != ''">question_stem = #{questionStem},</if>
            <if test="questionType != null and questionType != ''">question_type = #{questionType},</if>
            <if test="options != null and options != ''">options = #{options},</if>
            <if test="userAnswer != null">user_answer = #{userAnswer},</if>
            <if test="answerTime != null">answer_time = #{answerTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where answer_id = #{answerId}
    </update>

    <update id="updateEvaluationRecordAnswerByRecordAndQuestion" parameterType="EvaluationRecordAnswer">
        update evaluation_record_answer
        <trim prefix="SET" suffixOverrides=",">
            <if test="userAnswer != null">user_answer = #{userAnswer},</if>
            <if test="answerTime != null">answer_time = #{answerTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where record_id = #{recordId} and question_id = #{questionId}
    </update>

    <delete id="deleteEvaluationRecordAnswerByAnswerId" parameterType="Long">
        delete from evaluation_record_answer where answer_id = #{answerId}
    </delete>

    <delete id="deleteEvaluationRecordAnswerByAnswerIds" parameterType="String">
        delete from evaluation_record_answer where answer_id in 
        <foreach item="answerId" collection="array" open="(" separator="," close=")">
            #{answerId}
        </foreach>
    </delete>

    <delete id="deleteEvaluationRecordAnswerByRecordId" parameterType="Long">
        delete from evaluation_record_answer where record_id = #{recordId}
    </delete>
</mapper>
