<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.training.mapper.EvaluationRecordMapper">
    
    <resultMap type="EvaluationRecord" id="EvaluationRecordResult">
        <result property="recordId"    column="record_id"    />
        <result property="recordName"    column="record_name"    />
        <result property="studentId"    column="student_id"    />
        <result property="bankId"    column="bank_id"    />
        <result property="bankName"    column="bank_name"    />
        <result property="totalQuestions"    column="total_questions"    />
        <result property="answeredQuestions"    column="answered_questions"    />
        <result property="status"    column="status"    />
        <result property="startTime"    column="start_time"    />
        <result property="completeTime"    column="complete_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="studentName"    column="student_name"    />
    </resultMap>

    <sql id="selectEvaluationRecordVo">
        select er.record_id, er.record_name, er.student_id, er.bank_id, er.bank_name, 
               er.total_questions, er.answered_questions, er.status, er.start_time, er.complete_time, 
               er.create_by, er.create_time, er.update_by, er.update_time, er.remark,
               si.student_name
        from evaluation_record er
        left join student_info si on er.student_id = si.student_id
    </sql>

    <select id="selectEvaluationRecordList" parameterType="EvaluationRecord" resultMap="EvaluationRecordResult">
        <include refid="selectEvaluationRecordVo"/>
        <where>  
            <if test="recordName != null  and recordName != ''"> and er.record_name like concat('%', #{recordName}, '%')</if>
            <if test="studentId != null "> and er.student_id = #{studentId}</if>
            <if test="bankId != null "> and er.bank_id = #{bankId}</if>
            <if test="bankName != null  and bankName != ''"> and er.bank_name like concat('%', #{bankName}, '%')</if>
            <if test="status != null  and status != ''"> and er.status = #{status}</if>
            <if test="startTime != null "> and er.start_time &gt;= #{startTime}</if>
            <if test="completeTime != null "> and er.complete_time &lt;= #{completeTime}</if>
        </where>
        order by er.create_time desc
    </select>
    
    <select id="selectEvaluationRecordByRecordId" parameterType="Long" resultMap="EvaluationRecordResult">
        <include refid="selectEvaluationRecordVo"/>
        where er.record_id = #{recordId}
    </select>

    <select id="selectEvaluationRecordListByStudentId" parameterType="Long" resultMap="EvaluationRecordResult">
        <include refid="selectEvaluationRecordVo"/>
        where er.student_id = #{studentId}
        order by er.create_time desc
    </select>
        
    <insert id="insertEvaluationRecord" parameterType="EvaluationRecord" useGeneratedKeys="true" keyProperty="recordId">
        insert into evaluation_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recordName != null and recordName != ''">record_name,</if>
            <if test="studentId != null">student_id,</if>
            <if test="bankId != null">bank_id,</if>
            <if test="bankName != null and bankName != ''">bank_name,</if>
            <if test="totalQuestions != null">total_questions,</if>
            <if test="answeredQuestions != null">answered_questions,</if>
            <if test="status != null">status,</if>
            <if test="startTime != null">start_time,</if>
            <if test="completeTime != null">complete_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recordName != null and recordName != ''">#{recordName},</if>
            <if test="studentId != null">#{studentId},</if>
            <if test="bankId != null">#{bankId},</if>
            <if test="bankName != null and bankName != ''">#{bankName},</if>
            <if test="totalQuestions != null">#{totalQuestions},</if>
            <if test="answeredQuestions != null">#{answeredQuestions},</if>
            <if test="status != null">#{status},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="completeTime != null">#{completeTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateEvaluationRecord" parameterType="EvaluationRecord">
        update evaluation_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="recordName != null and recordName != ''">record_name = #{recordName},</if>
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="bankId != null">bank_id = #{bankId},</if>
            <if test="bankName != null and bankName != ''">bank_name = #{bankName},</if>
            <if test="totalQuestions != null">total_questions = #{totalQuestions},</if>
            <if test="answeredQuestions != null">answered_questions = #{answeredQuestions},</if>
            <if test="status != null">status = #{status},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="completeTime != null">complete_time = #{completeTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteEvaluationRecordByRecordId" parameterType="Long">
        delete from evaluation_record where record_id = #{recordId}
    </delete>

    <delete id="deleteEvaluationRecordByRecordIds" parameterType="String">
        delete from evaluation_record where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>
</mapper>
