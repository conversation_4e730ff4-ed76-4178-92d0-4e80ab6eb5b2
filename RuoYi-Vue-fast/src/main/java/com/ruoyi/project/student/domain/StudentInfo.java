package com.ruoyi.project.student.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 学生信息对象 student_info
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class StudentInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 学生ID */
    private Long studentId;

    /** 学生姓名 */
    @Excel(name = "学生姓名", sort = 1)
    @NotBlank(message = "学生姓名不能为空")
    @Size(min = 0, max = 50, message = "学生姓名长度不能超过50个字符")
    private String studentName;

    /** 学号 */
    @Excel(name = "学号", sort = 2)
    @NotBlank(message = "学号不能为空")
    @Size(min = 0, max = 50, message = "学号长度不能超过50个字符")
    private String studentNumber;

    /** 性别（0男 1女） */
    @Excel(name = "性别", sort = 3, readConverterExp = "0=男,1=女")
    @NotBlank(message = "性别不能为空")
    private String gender;

    /** 校内教师 */
    @Excel(name = "校内教师", sort = 4)
    @NotBlank(message = "校内教师不能为空")
    @Size(min = 0, max = 100, message = "校内教师长度不能超过100个字符")
    private String schoolTeacher;

    /** 企业导师 */
    @Excel(name = "企业导师", sort = 5)
    @NotBlank(message = "企业导师不能为空")
    @Size(min = 0, max = 100, message = "企业导师长度不能超过100个字符")
    private String companyTeacher;

    /** 专业 */
    @Excel(name = "专业", sort = 6)
    @NotBlank(message = "专业不能为空")
    @Size(min = 0, max = 100, message = "专业长度不能超过100个字符")
    private String major;

    /** 班级 */
    @Excel(name = "班级", sort = 7)
    @NotBlank(message = "班级不能为空")
    @Size(min = 0, max = 100, message = "班级长度不能超过100个字符")
    private String className;

    /** 目标岗位 */
    @Excel(name = "目标岗位", sort = 8)
    @NotBlank(message = "目标岗位不能为空")
    @Size(min = 0, max = 200, message = "目标岗位长度不能超过200个字符")
    private String targetPosition;

    /** 学生状态（0未面试 1面试失败 2成功签约） */
    private String studentStatus;

    /** 入驻企业 */
    private String residentCompany;

    /** 薪酬 */
    private java.math.BigDecimal salary;

    /** 关联合同ID */
    private Long contractId;

    /** 岗位能力达标阈值 */
    private Integer positionCompetencyThreshold;

    // 关联查询的合同信息（用于显示）
    /** 合同名称 */
    private String contractName;
    /** 合同编号 */
    private String contractNumber;
    /** 签约企业名称 */
    private String contractCompanyName;
    /** 签约日期 */
    private String contractSignDate;
    /** 岗位名称（来自合同） */
    private String postName;
    /** 薪酬（来自合同） */
    private java.math.BigDecimal contractSalary;

    public void setStudentId(Long studentId) 
    {
        this.studentId = studentId;
    }

    public Long getStudentId() 
    {
        return studentId;
    }

    public void setStudentName(String studentName) 
    {
        this.studentName = studentName;
    }

    public String getStudentName() 
    {
        return studentName;
    }

    public void setStudentNumber(String studentNumber) 
    {
        this.studentNumber = studentNumber;
    }

    public String getStudentNumber() 
    {
        return studentNumber;
    }

    public void setClassName(String className) 
    {
        this.className = className;
    }

    public String getClassName() 
    {
        return className;
    }

    public void setGender(String gender) 
    {
        this.gender = gender;
    }

    public String getGender() 
    {
        return gender;
    }

    public void setStudentStatus(String studentStatus) 
    {
        this.studentStatus = studentStatus;
    }

    public String getStudentStatus() 
    {
        return studentStatus;
    }

    public void setSchoolTeacher(String schoolTeacher) 
    {
        this.schoolTeacher = schoolTeacher;
    }

    public String getSchoolTeacher() 
    {
        return schoolTeacher;
    }

    public void setCompanyTeacher(String companyTeacher) 
    {
        this.companyTeacher = companyTeacher;
    }

    public String getCompanyTeacher() 
    {
        return companyTeacher;
    }

    public void setMajor(String major) 
    {
        this.major = major;
    }

    public String getMajor() 
    {
        return major;
    }

    public void setTargetPosition(String targetPosition) 
    {
        this.targetPosition = targetPosition;
    }

    public String getTargetPosition() 
    {
        return targetPosition;
    }

    public void setResidentCompany(String residentCompany) 
    {
        this.residentCompany = residentCompany;
    }

    public String getResidentCompany() 
    {
        return residentCompany;
    }

    public void setSalary(java.math.BigDecimal salary) 
    {
        this.salary = salary;
    }

    public java.math.BigDecimal getSalary() 
    {
        return salary;
    }

    public void setContractId(Long contractId) 
    {
        this.contractId = contractId;
    }

    public Long getContractId() 
    {
        return contractId;
    }

    public void setContractName(String contractName) 
    {
        this.contractName = contractName;
    }

    public String getContractName() 
    {
        return contractName;
    }

    public void setContractNumber(String contractNumber) 
    {
        this.contractNumber = contractNumber;
    }

    public String getContractNumber() 
    {
        return contractNumber;
    }

    public void setContractCompanyName(String contractCompanyName) 
    {
        this.contractCompanyName = contractCompanyName;
    }

    public String getContractCompanyName() 
    {
        return contractCompanyName;
    }

    public void setContractSignDate(String contractSignDate) 
    {
        this.contractSignDate = contractSignDate;
    }

    public String getContractSignDate() 
    {
        return contractSignDate;
    }

    public void setPostName(String postName) 
    {
        this.postName = postName;
    }

    public String getPostName() 
    {
        return postName;
    }

    public void setContractSalary(java.math.BigDecimal contractSalary) 
    {
        this.contractSalary = contractSalary;
    }

    public java.math.BigDecimal getContractSalary()
    {
        return contractSalary;
    }

    public void setPositionCompetencyThreshold(Integer positionCompetencyThreshold)
    {
        this.positionCompetencyThreshold = positionCompetencyThreshold;
    }

    public Integer getPositionCompetencyThreshold()
    {
        return positionCompetencyThreshold;
    }

    @Override
    public String toString() {
        return new StringBuilder()
            .append("StudentInfo{")
            .append("studentId=").append(studentId)
            .append(", studentName='").append(studentName).append('\'')
            .append(", studentNumber='").append(studentNumber).append('\'')
            .append(", className='").append(className).append('\'')
            .append(", gender='").append(gender).append('\'')
            .append(", studentStatus='").append(studentStatus).append('\'')
            .append(", schoolTeacher='").append(schoolTeacher).append('\'')
            .append(", companyTeacher='").append(companyTeacher).append('\'')
            .append(", major='").append(major).append('\'')
            .append(", targetPosition='").append(targetPosition).append('\'')
            .append(", residentCompany='").append(residentCompany).append('\'')
            .append(", salary=").append(salary)
            .append("}")
            .toString();
    }
} 