package com.ruoyi.project.student.service.impl;

import java.util.List;
import javax.validation.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanValidators;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.project.student.domain.StudentInfo;
import com.ruoyi.project.student.mapper.StudentInfoMapper;
import com.ruoyi.project.student.service.IStudentInfoService;
import com.ruoyi.project.student.service.IStudentGrowthPathService;
import com.ruoyi.project.student.service.IStudentExamScoreService;

/**
 * 学生信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class StudentInfoServiceImpl extends BaseController implements IStudentInfoService 
{
    private static final Logger log = LoggerFactory.getLogger(StudentInfoServiceImpl.class);

    @Autowired
    private StudentInfoMapper studentInfoMapper;

    @Autowired
    private IStudentGrowthPathService studentGrowthPathService;

    @Autowired
    private IStudentExamScoreService studentExamScoreService;

    @Autowired
    protected Validator validator;

    /**
     * 查询学生信息
     * 
     * @param studentId 学生信息主键
     * @return 学生信息
     */
    @Override
    public StudentInfo selectStudentInfoByStudentId(Long studentId)
    {
        return studentInfoMapper.selectStudentInfoByStudentId(studentId);
    }

    /**
     * 查询学生信息列表
     * 
     * @param studentInfo 学生信息
     * @return 学生信息
     */
    @Override
    public List<StudentInfo> selectStudentInfoList(StudentInfo studentInfo)
    {
        return studentInfoMapper.selectStudentInfoList(studentInfo);
    }

    /**
     * 新增学生信息
     * 
     * @param studentInfo 学生信息
     * @return 结果
     */
    @Override
    public int insertStudentInfo(StudentInfo studentInfo)
    {
        // 设置创建人和创建时间
        studentInfo.setCreateBy(getUsername());
        studentInfo.setCreateTime(DateUtils.getNowDate());
        return studentInfoMapper.insertStudentInfo(studentInfo);
    }

    /**
     * 修改学生信息
     * 
     * @param studentInfo 学生信息
     * @return 结果
     */
    @Override
    public int updateStudentInfo(StudentInfo studentInfo)
    {
        // 设置更新人和更新时间
        studentInfo.setUpdateBy(getUsername());
        studentInfo.setUpdateTime(DateUtils.getNowDate());
        return studentInfoMapper.updateStudentInfo(studentInfo);
    }

    /**
     * 批量删除学生信息
     * 
     * @param studentIds 需要删除的学生信息主键
     * @return 结果
     */
    @Override
    public int deleteStudentInfoByStudentIds(Long[] studentIds)
    {
        // 先删除关联的成长路径数据
        studentGrowthPathService.deleteStudentGrowthPathByStudentIds(studentIds);
        
        // 再删除关联的考试成绩数据
        studentExamScoreService.deleteStudentExamScoreByStudentIds(studentIds);
        
        // 最后删除学生信息
        return studentInfoMapper.deleteStudentInfoByStudentIds(studentIds);
    }

    /**
     * 删除学生信息信息
     * 
     * @param studentId 学生信息主键
     * @return 结果
     */
    @Override
    public int deleteStudentInfoByStudentId(Long studentId)
    {
        // 先删除关联的成长路径数据
        studentGrowthPathService.deleteStudentGrowthPathByStudentId(studentId);
        
        // 再删除关联的考试成绩数据
        studentExamScoreService.deleteStudentExamScoreByStudentId(studentId);
        
        // 最后删除学生信息
        return studentInfoMapper.deleteStudentInfoByStudentId(studentId);
    }

    /**
     * 校验学号是否唯一
     * 
     * @param studentInfo 学生信息
     * @return 结果
     */
    @Override
    public boolean checkStudentNumberUnique(StudentInfo studentInfo)
    {
        Long studentId = StringUtils.isNull(studentInfo.getStudentId()) ? -1L : studentInfo.getStudentId();
        StudentInfo info = studentInfoMapper.checkStudentNumberUnique(studentInfo.getStudentNumber());
        if (StringUtils.isNotNull(info) && info.getStudentId().longValue() != studentId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 导入学生信息数据
     *
     * @param studentInfoList 学生信息数据列表
     * @param isUpdateSupport 是否覆盖数据，如果为true则先删除所有学生信息再导入
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importStudentInfo(List<StudentInfo> studentInfoList, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(studentInfoList) || studentInfoList.size() == 0)
        {
            throw new ServiceException("导入学生信息数据不能为空！");
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        // 如果选择覆盖数据，先删除所有学生信息
        if (isUpdateSupport)
        {
            try
            {
                // 查询所有学生ID
                List<StudentInfo> allStudents = studentInfoMapper.selectStudentInfoList(new StudentInfo());
                if (!allStudents.isEmpty())
                {
                    Long[] allStudentIds = allStudents.stream()
                        .map(StudentInfo::getStudentId)
                        .toArray(Long[]::new);

                    // 删除所有学生信息（包括关联的成长路径和考试成绩）
                    this.deleteStudentInfoByStudentIds(allStudentIds);
                    log.info("覆盖模式：已删除所有现有学生信息，共 {} 条", allStudentIds.length);
                }
            }
            catch (Exception e)
            {
                log.error("删除现有学生信息失败", e);
                throw new ServiceException("覆盖导入失败：删除现有数据时出错");
            }
        }

        // 导入新数据
        for (StudentInfo studentInfo : studentInfoList)
        {
            try
            {
                // 验证数据
                BeanValidators.validateWithException(validator, studentInfo);

                if (!isUpdateSupport)
                {
                    // 非覆盖模式：检查学号是否已存在
                    StudentInfo existingStudent = studentInfoMapper.checkStudentNumberUnique(studentInfo.getStudentNumber());
                    if (!StringUtils.isNull(existingStudent))
                    {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、学号 " + studentInfo.getStudentNumber() + " 已存在，跳过导入");
                        continue;
                    }
                }

                // 设置创建人和创建时间
                studentInfo.setCreateBy(operName);
                studentInfo.setCreateTime(DateUtils.getNowDate());
                this.insertStudentInfo(studentInfo);
                successNum++;
                successMsg.append("<br/>" + successNum + "、学号 " + studentInfo.getStudentNumber() + " 导入成功");
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、学号 " + studentInfo.getStudentNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }

        if (failureNum > 0)
        {
            String mode = isUpdateSupport ? "覆盖" : "增量";
            failureMsg.insert(0, "很抱歉，" + mode + "导入部分失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            if (successNum > 0)
            {
                successMsg.insert(0, "成功导入 " + successNum + " 条数据，数据如下：");
                return successMsg.toString() + failureMsg.toString();
            }
            else
            {
                throw new ServiceException(failureMsg.toString());
            }
        }
        else
        {
            String mode = isUpdateSupport ? "覆盖" : "增量";
            successMsg.insert(0, "恭喜您，" + mode + "导入全部成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
} 