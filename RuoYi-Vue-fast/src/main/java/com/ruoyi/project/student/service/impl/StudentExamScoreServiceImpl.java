package com.ruoyi.project.student.service.impl;

import java.util.List;
import java.util.Set;
import java.util.HashSet;
import javax.validation.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.exception.ServiceException;

import com.ruoyi.common.utils.bean.BeanValidators;
import com.ruoyi.framework.web.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.student.mapper.StudentExamScoreMapper;
import com.ruoyi.project.student.mapper.StudentInfoMapper;
import com.ruoyi.project.student.domain.StudentExamScore;
import com.ruoyi.project.student.domain.StudentExamScoreImport;
import com.ruoyi.project.student.domain.StudentInfo;
import com.ruoyi.project.student.service.IStudentExamScoreService;

/**
 * 学生考试成绩Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class StudentExamScoreServiceImpl extends BaseController implements IStudentExamScoreService
{
    private static final Logger log = LoggerFactory.getLogger(StudentExamScoreServiceImpl.class);

    @Autowired
    private StudentExamScoreMapper studentExamScoreMapper;

    @Autowired
    private StudentInfoMapper studentInfoMapper;

    @Autowired
    protected Validator validator;

    /**
     * 查询学生考试成绩
     * 
     * @param scoreId 学生考试成绩主键
     * @return 学生考试成绩
     */
    @Override
    public StudentExamScore selectStudentExamScoreByScoreId(Long scoreId)
    {
        return studentExamScoreMapper.selectStudentExamScoreByScoreId(scoreId);
    }

    /**
     * 查询学生考试成绩列表
     * 
     * @param studentExamScore 学生考试成绩
     * @return 学生考试成绩
     */
    @Override
    public List<StudentExamScore> selectStudentExamScoreList(StudentExamScore studentExamScore)
    {
        return studentExamScoreMapper.selectStudentExamScoreList(studentExamScore);
    }

    /**
     * 根据学生ID查询考试成绩列表
     * 
     * @param studentId 学生ID
     * @return 学生考试成绩集合
     */
    @Override
    public List<StudentExamScore> selectStudentExamScoreListByStudentId(Long studentId)
    {
        return studentExamScoreMapper.selectStudentExamScoreListByStudentId(studentId);
    }

    /**
     * 新增学生考试成绩
     * 
     * @param studentExamScore 学生考试成绩
     * @return 结果
     */
    @Override
    public int insertStudentExamScore(StudentExamScore studentExamScore)
    {
        studentExamScore.setCreateBy(getUsername());
        studentExamScore.setCreateTime(DateUtils.getNowDate());
        return studentExamScoreMapper.insertStudentExamScore(studentExamScore);
    }

    /**
     * 修改学生考试成绩
     * 
     * @param studentExamScore 学生考试成绩
     * @return 结果
     */
    @Override
    public int updateStudentExamScore(StudentExamScore studentExamScore)
    {
        studentExamScore.setUpdateBy(getUsername());
        studentExamScore.setUpdateTime(DateUtils.getNowDate());
        return studentExamScoreMapper.updateStudentExamScore(studentExamScore);
    }

    /**
     * 批量删除学生考试成绩
     * 
     * @param scoreIds 需要删除的学生考试成绩主键
     * @return 结果
     */
    @Override
    public int deleteStudentExamScoreByScoreIds(Long[] scoreIds)
    {
        return studentExamScoreMapper.deleteStudentExamScoreByScoreIds(scoreIds);
    }

    /**
     * 删除学生考试成绩信息
     * 
     * @param scoreId 学生考试成绩主键
     * @return 结果
     */
    @Override
    public int deleteStudentExamScoreByScoreId(Long scoreId)
    {
        return studentExamScoreMapper.deleteStudentExamScoreByScoreId(scoreId);
    }

    /**
     * 根据学生ID删除考试成绩
     * 
     * @param studentId 学生ID
     * @return 结果
     */
    @Override
    public int deleteStudentExamScoreByStudentId(Long studentId)
    {
        return studentExamScoreMapper.deleteStudentExamScoreByStudentId(studentId);
    }

    /**
     * 根据学生ID批量删除考试成绩
     * 
     * @param studentIds 学生ID数组
     * @return 结果
     */
    @Override
    public int deleteStudentExamScoreByStudentIds(Long[] studentIds)
    {
        return studentExamScoreMapper.deleteStudentExamScoreByStudentIds(studentIds);
    }

    /**
     * 导入学生考试成绩数据
     *
     * @param scoreImportList 成绩导入数据列表
     * @param courseType 课程类型（0校内课程 1企业课程）
     * @param isUpdateSupport 是否覆盖数据，如果为true则先删除涉及学生的所有该类型成绩再导入
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importStudentExamScore(List<StudentExamScoreImport> scoreImportList, String courseType, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(scoreImportList) || scoreImportList.size() == 0)
        {
            throw new ServiceException("导入学生成绩数据不能为空！");
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        String courseTypeName = "0".equals(courseType) ? "校内课程" : "企业课程";

        // 如果选择覆盖数据，先删除涉及学生的所有该类型成绩
        if (isUpdateSupport)
        {
            try
            {
                // 收集所有涉及的学生ID
                Set<Long> involvedStudentIds = new HashSet<>();
                for (StudentExamScoreImport scoreImport : scoreImportList)
                {
                    StudentInfo student = studentInfoMapper.checkStudentNumberUnique(scoreImport.getStudentNumber());
                    if (!StringUtils.isNull(student))
                    {
                        involvedStudentIds.add(student.getStudentId());
                    }
                }

                // 删除这些学生的所有该类型成绩
                if (!involvedStudentIds.isEmpty())
                {
                    int deletedCount = deleteScoresByStudentIdsAndCourseType(involvedStudentIds, courseType);
                    log.info("覆盖模式：已删除 {} 个学生的所有{}成绩，共 {} 条",
                        involvedStudentIds.size(), courseTypeName, deletedCount);
                }
            }
            catch (Exception e)
            {
                log.error("删除现有成绩数据失败", e);
                throw new ServiceException("覆盖导入失败：删除现有成绩数据时出错");
            }
        }

        // 导入新成绩数据
        for (StudentExamScoreImport scoreImport : scoreImportList)
        {
            try
            {
                // 验证是否存在这个学生
                StudentInfo student = studentInfoMapper.checkStudentNumberUnique(scoreImport.getStudentNumber());
                if (StringUtils.isNull(student))
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、学号 " + scoreImport.getStudentNumber() + " 导入失败：学生不存在");
                    continue;
                }

                // 验证导入数据
                BeanValidators.validateWithException(validator, scoreImport);

                if (!isUpdateSupport)
                {
                    // 非覆盖模式：检查是否已存在相同的成绩记录
                    StudentExamScore existingScore = findExistingScore(student.getStudentId(),
                        scoreImport.getCourseName(), courseType, scoreImport.getExamType());
                    if (!StringUtils.isNull(existingScore))
                    {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、学号 " + scoreImport.getStudentNumber() +
                            " 课程《" + scoreImport.getCourseName() + "》" + courseTypeName + "成绩已存在，跳过导入");
                        continue;
                    }
                }

                // 创建成绩对象
                StudentExamScore examScore = new StudentExamScore();
                examScore.setStudentId(student.getStudentId());
                examScore.setCourseName(scoreImport.getCourseName());
                examScore.setCourseType(courseType);
                examScore.setExamType(scoreImport.getExamType());
                examScore.setScore(scoreImport.getScore());
                examScore.setCreateBy(operName);
                examScore.setCreateTime(DateUtils.getNowDate());

                // 新增成绩
                this.insertStudentExamScore(examScore);
                successNum++;
                successMsg.append("<br/>" + successNum + "、学号 " + scoreImport.getStudentNumber() +
                    " 课程《" + scoreImport.getCourseName() + "》" + courseTypeName + "成绩导入成功");
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、学号 " + scoreImport.getStudentNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }

        if (failureNum > 0)
        {
            String mode = isUpdateSupport ? "覆盖" : "增量";
            failureMsg.insert(0, "很抱歉，" + mode + "导入部分失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            if (successNum > 0)
            {
                successMsg.insert(0, "成功导入 " + successNum + " 条" + courseTypeName + "成绩，数据如下：");
                return successMsg.toString() + failureMsg.toString();
            }
            else
            {
                throw new ServiceException(failureMsg.toString());
            }
        }
        else
        {
            String mode = isUpdateSupport ? "覆盖" : "增量";
            successMsg.insert(0, "恭喜您，" + mode + "导入全部成功！共 " + successNum + " 条" + courseTypeName + "成绩，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 查找已存在的成绩记录
     */
    private StudentExamScore findExistingScore(Long studentId, String courseName, String courseType, String examType)
    {
        StudentExamScore queryScore = new StudentExamScore();
        queryScore.setStudentId(studentId);
        queryScore.setCourseName(courseName);
        queryScore.setCourseType(courseType);
        queryScore.setExamType(examType);

        List<StudentExamScore> existingScores = studentExamScoreMapper.selectStudentExamScoreList(queryScore);
        return existingScores.isEmpty() ? null : existingScores.get(0);
    }

    /**
     * 根据学生ID集合和课程类型删除成绩
     */
    private int deleteScoresByStudentIdsAndCourseType(Set<Long> studentIds, String courseType)
    {
        int totalDeleted = 0;
        for (Long studentId : studentIds)
        {
            StudentExamScore queryScore = new StudentExamScore();
            queryScore.setStudentId(studentId);
            queryScore.setCourseType(courseType);

            // 查询该学生该类型的所有成绩
            List<StudentExamScore> scores = studentExamScoreMapper.selectStudentExamScoreList(queryScore);
            if (!scores.isEmpty())
            {
                Long[] scoreIds = scores.stream()
                    .map(StudentExamScore::getScoreId)
                    .toArray(Long[]::new);

                // 删除这些成绩
                int deleted = studentExamScoreMapper.deleteStudentExamScoreByScoreIds(scoreIds);
                totalDeleted += deleted;
            }
        }
        return totalDeleted;
    }
}