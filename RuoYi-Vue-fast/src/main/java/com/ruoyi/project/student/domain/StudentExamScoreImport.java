package com.ruoyi.project.student.domain;

import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;

/**
 * 学生考试成绩导入对象 student_exam_score_import
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class StudentExamScoreImport
{
    private static final long serialVersionUID = 1L;

    /** 学生学号 */
    @Excel(name = "学生学号", sort = 1)
    @NotBlank(message = "学生学号不能为空")
    @Size(min = 0, max = 50, message = "学生学号长度不能超过50个字符")
    private String studentNumber;

    /** 课程名称 */
    @Excel(name = "课程名称", sort = 2)
    @NotBlank(message = "课程名称不能为空")
    @Size(min = 0, max = 100, message = "课程名称长度不能超过100个字符")
    private String courseName;

    /** 考试类型 */
    @Excel(name = "考试类型", sort = 3)
    @Size(min = 0, max = 50, message = "考试类型长度不能超过50个字符")
    private String examType;

    /** 成绩 */
    @Excel(name = "成绩", sort = 4)
    @NotNull(message = "成绩不能为空")
    private BigDecimal score;

    public void setStudentNumber(String studentNumber) 
    {
        this.studentNumber = studentNumber;
    }

    public String getStudentNumber() 
    {
        return studentNumber;
    }

    public void setCourseName(String courseName) 
    {
        this.courseName = courseName;
    }

    public String getCourseName() 
    {
        return courseName;
    }

    public void setExamType(String examType) 
    {
        this.examType = examType;
    }

    public String getExamType() 
    {
        return examType;
    }

    public void setScore(BigDecimal score) 
    {
        this.score = score;
    }

    public BigDecimal getScore() 
    {
        return score;
    }

    @Override
    public String toString() {
        return "StudentExamScoreImport{" +
                "studentNumber='" + studentNumber + '\'' +
                ", courseName='" + courseName + '\'' +
                ", examType='" + examType + '\'' +
                ", score=" + score +
                '}';
    }
}
