package com.ruoyi.project.student.service;

import java.util.List;
import com.ruoyi.project.student.domain.StudentExamScore;
import com.ruoyi.project.student.domain.StudentExamScoreImport;

/**
 * 学生考试成绩Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IStudentExamScoreService 
{
    /**
     * 查询学生考试成绩
     * 
     * @param scoreId 学生考试成绩主键
     * @return 学生考试成绩
     */
    public StudentExamScore selectStudentExamScoreByScoreId(Long scoreId);

    /**
     * 查询学生考试成绩列表
     * 
     * @param studentExamScore 学生考试成绩
     * @return 学生考试成绩集合
     */
    public List<StudentExamScore> selectStudentExamScoreList(StudentExamScore studentExamScore);

    /**
     * 根据学生ID查询考试成绩列表
     * 
     * @param studentId 学生ID
     * @return 学生考试成绩集合
     */
    public List<StudentExamScore> selectStudentExamScoreListByStudentId(Long studentId);

    /**
     * 新增学生考试成绩
     * 
     * @param studentExamScore 学生考试成绩
     * @return 结果
     */
    public int insertStudentExamScore(StudentExamScore studentExamScore);

    /**
     * 修改学生考试成绩
     * 
     * @param studentExamScore 学生考试成绩
     * @return 结果
     */
    public int updateStudentExamScore(StudentExamScore studentExamScore);

    /**
     * 批量删除学生考试成绩
     * 
     * @param scoreIds 需要删除的学生考试成绩主键集合
     * @return 结果
     */
    public int deleteStudentExamScoreByScoreIds(Long[] scoreIds);

    /**
     * 删除学生考试成绩信息
     * 
     * @param scoreId 学生考试成绩主键
     * @return 结果
     */
    public int deleteStudentExamScoreByScoreId(Long scoreId);

    /**
     * 根据学生ID删除考试成绩
     * 
     * @param studentId 学生ID
     * @return 结果
     */
    public int deleteStudentExamScoreByStudentId(Long studentId);

    /**
     * 根据学生ID批量删除考试成绩
     *
     * @param studentIds 学生ID数组
     * @return 结果
     */
    public int deleteStudentExamScoreByStudentIds(Long[] studentIds);

    /**
     * 导入学生考试成绩数据
     *
     * @param scoreImportList 成绩导入数据列表
     * @param courseType 课程类型（0校内课程 1企业课程）
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importStudentExamScore(List<StudentExamScoreImport> scoreImportList, String courseType, Boolean isUpdateSupport, String operName);
}