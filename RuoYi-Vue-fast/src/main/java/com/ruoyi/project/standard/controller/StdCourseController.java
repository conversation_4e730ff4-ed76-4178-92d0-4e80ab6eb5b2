package com.ruoyi.project.standard.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.standard.domain.StdCourse;
import com.ruoyi.project.standard.service.IStdCourseService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 课程Controller
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@RestController
@RequestMapping("/standard/course")
public class StdCourseController extends BaseController
{
    @Autowired
    private IStdCourseService stdCourseService;

    /**
     * 查询课程列表
     */
    @PreAuthorize("@ss.hasPermi('standard:course:list')")
    @GetMapping("/list")
    public TableDataInfo list(StdCourse stdCourse)
    {
        startPage();
        List<StdCourse> list = stdCourseService.selectStdCourseList(stdCourse);
        return getDataTable(list);
    }

    /**
     * 导出课程列表
     */
    @PreAuthorize("@ss.hasPermi('standard:course:export')")
    @Log(title = "课程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StdCourse stdCourse)
    {
        List<StdCourse> list = stdCourseService.selectStdCourseList(stdCourse);
        ExcelUtil<StdCourse> util = new ExcelUtil<StdCourse>(StdCourse.class);
        util.exportExcel(response, list, "课程数据");
    }

    /**
     * 获取课程详细信息
     */
    @PreAuthorize("@ss.hasPermi('standard:course:query')")
    @GetMapping(value = "/{courseId}")
    public AjaxResult getInfo(@PathVariable("courseId") Long courseId)
    {
        return success(stdCourseService.selectStdCourseByCourseId(courseId));
    }

    /**
     * 新增课程
     */
    @PreAuthorize("@ss.hasPermi('standard:course:add')")
    @Log(title = "课程", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StdCourse stdCourse)
    {
        return toAjax(stdCourseService.insertStdCourse(stdCourse));
    }

    /**
     * 修改课程
     */
    @PreAuthorize("@ss.hasPermi('standard:course:edit')")
    @Log(title = "课程", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StdCourse stdCourse)
    {
        return toAjax(stdCourseService.updateStdCourse(stdCourse));
    }

    /**
     * 删除课程
     */
    @PreAuthorize("@ss.hasPermi('standard:course:remove')")
    @Log(title = "课程", businessType = BusinessType.DELETE)
	@DeleteMapping("/{courseIds}")
    public AjaxResult remove(@PathVariable Long[] courseIds)
    {
        return toAjax(stdCourseService.deleteStdCourseByCourseIds(courseIds));
    }
}
