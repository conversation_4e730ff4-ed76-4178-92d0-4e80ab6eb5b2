package com.ruoyi.project.standard.mapper;

import java.util.List;
import com.ruoyi.project.standard.domain.StdRegulation;

/**
 * 制度Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public interface StdRegulationMapper 
{
    /**
     * 查询制度
     * 
     * @param regulationId 制度主键
     * @return 制度
     */
    public StdRegulation selectStdRegulationByRegulationId(Long regulationId);

    /**
     * 查询制度列表
     * 
     * @param stdRegulation 制度
     * @return 制度集合
     */
    public List<StdRegulation> selectStdRegulationList(StdRegulation stdRegulation);

    /**
     * 新增制度
     * 
     * @param stdRegulation 制度
     * @return 结果
     */
    public int insertStdRegulation(StdRegulation stdRegulation);

    /**
     * 修改制度
     * 
     * @param stdRegulation 制度
     * @return 结果
     */
    public int updateStdRegulation(StdRegulation stdRegulation);

    /**
     * 删除制度
     * 
     * @param regulationId 制度主键
     * @return 结果
     */
    public int deleteStdRegulationByRegulationId(Long regulationId);

    /**
     * 批量删除制度
     * 
     * @param regulationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStdRegulationByRegulationIds(Long[] regulationIds);
}
