package com.ruoyi.project.standard.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 制度对象 std_regulation
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public class StdRegulation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 制度id */
    private Long regulationId;

    /** 制度名称 */
    @Excel(name = "制度名称")
    private String regulationName;

    /** 附件路径 */
    @Excel(name = "附件路径")
    private String attachment;

    public void setRegulationId(Long regulationId) 
    {
        this.regulationId = regulationId;
    }

    public Long getRegulationId() 
    {
        return regulationId;
    }

    public void setRegulationName(String regulationName) 
    {
        this.regulationName = regulationName;
    }

    public String getRegulationName() 
    {
        return regulationName;
    }

    public void setAttachment(String attachment) 
    {
        this.attachment = attachment;
    }

    public String getAttachment() 
    {
        return attachment;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("regulationId", getRegulationId())
            .append("regulationName", getRegulationName())
            .append("attachment", getAttachment())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
