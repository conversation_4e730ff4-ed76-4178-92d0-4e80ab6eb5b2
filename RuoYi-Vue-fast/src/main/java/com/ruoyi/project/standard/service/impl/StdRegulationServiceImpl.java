package com.ruoyi.project.standard.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.standard.mapper.StdRegulationMapper;
import com.ruoyi.project.standard.domain.StdRegulation;
import com.ruoyi.project.standard.service.IStdRegulationService;

/**
 * 制度Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Service
public class StdRegulationServiceImpl implements IStdRegulationService 
{
    @Autowired
    private StdRegulationMapper stdRegulationMapper;

    /**
     * 查询制度
     * 
     * @param regulationId 制度主键
     * @return 制度
     */
    @Override
    public StdRegulation selectStdRegulationByRegulationId(Long regulationId)
    {
        return stdRegulationMapper.selectStdRegulationByRegulationId(regulationId);
    }

    /**
     * 查询制度列表
     * 
     * @param stdRegulation 制度
     * @return 制度
     */
    @Override
    public List<StdRegulation> selectStdRegulationList(StdRegulation stdRegulation)
    {
        return stdRegulationMapper.selectStdRegulationList(stdRegulation);
    }

    /**
     * 新增制度
     * 
     * @param stdRegulation 制度
     * @return 结果
     */
    @Override
    public int insertStdRegulation(StdRegulation stdRegulation)
    {
        stdRegulation.setCreateTime(DateUtils.getNowDate());
        return stdRegulationMapper.insertStdRegulation(stdRegulation);
    }

    /**
     * 修改制度
     * 
     * @param stdRegulation 制度
     * @return 结果
     */
    @Override
    public int updateStdRegulation(StdRegulation stdRegulation)
    {
        stdRegulation.setUpdateTime(DateUtils.getNowDate());
        return stdRegulationMapper.updateStdRegulation(stdRegulation);
    }

    /**
     * 批量删除制度
     * 
     * @param regulationIds 需要删除的制度主键
     * @return 结果
     */
    @Override
    public int deleteStdRegulationByRegulationIds(Long[] regulationIds)
    {
        return stdRegulationMapper.deleteStdRegulationByRegulationIds(regulationIds);
    }

    /**
     * 删除制度信息
     * 
     * @param regulationId 制度主键
     * @return 结果
     */
    @Override
    public int deleteStdRegulationByRegulationId(Long regulationId)
    {
        return stdRegulationMapper.deleteStdRegulationByRegulationId(regulationId);
    }
}
