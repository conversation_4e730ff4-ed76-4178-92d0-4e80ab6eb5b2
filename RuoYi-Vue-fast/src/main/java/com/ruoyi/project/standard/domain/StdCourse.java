package com.ruoyi.project.standard.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 课程对象 std_course
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public class StdCourse extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 课程id */
    private Long courseId;

    /** 课程名称 */
    @Excel(name = "课程名称")
    private String courseName;

    /** 标准描述 */
    @Excel(name = "标准描述")
    private String standardDesc;

    /** 培养规格 */
    @Excel(name = "培养规格")
    private String trainingSpec;

    public void setCourseId(Long courseId) 
    {
        this.courseId = courseId;
    }

    public Long getCourseId() 
    {
        return courseId;
    }

    public void setCourseName(String courseName) 
    {
        this.courseName = courseName;
    }

    public String getCourseName() 
    {
        return courseName;
    }

    public void setStandardDesc(String standardDesc) 
    {
        this.standardDesc = standardDesc;
    }

    public String getStandardDesc() 
    {
        return standardDesc;
    }

    public void setTrainingSpec(String trainingSpec) 
    {
        this.trainingSpec = trainingSpec;
    }

    public String getTrainingSpec() 
    {
        return trainingSpec;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("courseId", getCourseId())
            .append("courseName", getCourseName())
            .append("standardDesc", getStandardDesc())
            .append("trainingSpec", getTrainingSpec())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
