package com.ruoyi.project.standard.mapper;

import java.util.List;
import com.ruoyi.project.standard.domain.StdCourse;

/**
 * 课程Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public interface StdCourseMapper 
{
    /**
     * 查询课程
     * 
     * @param courseId 课程主键
     * @return 课程
     */
    public StdCourse selectStdCourseByCourseId(Long courseId);

    /**
     * 查询课程列表
     * 
     * @param stdCourse 课程
     * @return 课程集合
     */
    public List<StdCourse> selectStdCourseList(StdCourse stdCourse);

    /**
     * 新增课程
     * 
     * @param stdCourse 课程
     * @return 结果
     */
    public int insertStdCourse(StdCourse stdCourse);

    /**
     * 修改课程
     * 
     * @param stdCourse 课程
     * @return 结果
     */
    public int updateStdCourse(StdCourse stdCourse);

    /**
     * 删除课程
     * 
     * @param courseId 课程主键
     * @return 结果
     */
    public int deleteStdCourseByCourseId(Long courseId);

    /**
     * 批量删除课程
     * 
     * @param courseIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStdCourseByCourseIds(Long[] courseIds);
}
