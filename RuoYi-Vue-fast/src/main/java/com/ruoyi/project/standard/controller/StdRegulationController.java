package com.ruoyi.project.standard.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.standard.domain.StdRegulation;
import com.ruoyi.project.standard.service.IStdRegulationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 制度Controller
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@RestController
@RequestMapping("/standard/regulation")
public class StdRegulationController extends BaseController
{
    @Autowired
    private IStdRegulationService stdRegulationService;

    /**
     * 查询制度列表
     */
    @PreAuthorize("@ss.hasPermi('standard:regulation:list')")
    @GetMapping("/list")
    public TableDataInfo list(StdRegulation stdRegulation)
    {
        startPage();
        List<StdRegulation> list = stdRegulationService.selectStdRegulationList(stdRegulation);
        return getDataTable(list);
    }

    /**
     * 导出制度列表
     */
    @PreAuthorize("@ss.hasPermi('standard:regulation:export')")
    @Log(title = "制度", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StdRegulation stdRegulation)
    {
        List<StdRegulation> list = stdRegulationService.selectStdRegulationList(stdRegulation);
        ExcelUtil<StdRegulation> util = new ExcelUtil<StdRegulation>(StdRegulation.class);
        util.exportExcel(response, list, "制度数据");
    }

    /**
     * 获取制度详细信息
     */
    @PreAuthorize("@ss.hasPermi('standard:regulation:query')")
    @GetMapping(value = "/{regulationId}")
    public AjaxResult getInfo(@PathVariable("regulationId") Long regulationId)
    {
        return success(stdRegulationService.selectStdRegulationByRegulationId(regulationId));
    }

    /**
     * 新增制度
     */
    @PreAuthorize("@ss.hasPermi('standard:regulation:add')")
    @Log(title = "制度", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StdRegulation stdRegulation)
    {
        return toAjax(stdRegulationService.insertStdRegulation(stdRegulation));
    }

    /**
     * 修改制度
     */
    @PreAuthorize("@ss.hasPermi('standard:regulation:edit')")
    @Log(title = "制度", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StdRegulation stdRegulation)
    {
        return toAjax(stdRegulationService.updateStdRegulation(stdRegulation));
    }

    /**
     * 删除制度
     */
    @PreAuthorize("@ss.hasPermi('standard:regulation:remove')")
    @Log(title = "制度", businessType = BusinessType.DELETE)
	@DeleteMapping("/{regulationIds}")
    public AjaxResult remove(@PathVariable Long[] regulationIds)
    {
        return toAjax(stdRegulationService.deleteStdRegulationByRegulationIds(regulationIds));
    }
}
