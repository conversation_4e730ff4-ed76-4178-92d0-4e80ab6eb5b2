package com.ruoyi.project.standard.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.standard.mapper.StdCourseMapper;
import com.ruoyi.project.standard.domain.StdCourse;
import com.ruoyi.project.standard.service.IStdCourseService;

/**
 * 课程Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Service
public class StdCourseServiceImpl implements IStdCourseService 
{
    @Autowired
    private StdCourseMapper stdCourseMapper;

    /**
     * 查询课程
     * 
     * @param courseId 课程主键
     * @return 课程
     */
    @Override
    public StdCourse selectStdCourseByCourseId(Long courseId)
    {
        return stdCourseMapper.selectStdCourseByCourseId(courseId);
    }

    /**
     * 查询课程列表
     * 
     * @param stdCourse 课程
     * @return 课程
     */
    @Override
    public List<StdCourse> selectStdCourseList(StdCourse stdCourse)
    {
        return stdCourseMapper.selectStdCourseList(stdCourse);
    }

    /**
     * 新增课程
     * 
     * @param stdCourse 课程
     * @return 结果
     */
    @Override
    public int insertStdCourse(StdCourse stdCourse)
    {
        stdCourse.setCreateTime(DateUtils.getNowDate());
        return stdCourseMapper.insertStdCourse(stdCourse);
    }

    /**
     * 修改课程
     * 
     * @param stdCourse 课程
     * @return 结果
     */
    @Override
    public int updateStdCourse(StdCourse stdCourse)
    {
        stdCourse.setUpdateTime(DateUtils.getNowDate());
        return stdCourseMapper.updateStdCourse(stdCourse);
    }

    /**
     * 批量删除课程
     * 
     * @param courseIds 需要删除的课程主键
     * @return 结果
     */
    @Override
    public int deleteStdCourseByCourseIds(Long[] courseIds)
    {
        return stdCourseMapper.deleteStdCourseByCourseIds(courseIds);
    }

    /**
     * 删除课程信息
     * 
     * @param courseId 课程主键
     * @return 结果
     */
    @Override
    public int deleteStdCourseByCourseId(Long courseId)
    {
        return stdCourseMapper.deleteStdCourseByCourseId(courseId);
    }
}
