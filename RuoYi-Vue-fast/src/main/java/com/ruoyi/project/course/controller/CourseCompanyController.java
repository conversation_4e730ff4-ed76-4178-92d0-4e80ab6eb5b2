package com.ruoyi.project.course.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.course.domain.CourseCompany;
import com.ruoyi.project.course.service.ICourseCompanyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 企业课程Controller
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@RestController
@RequestMapping("/course/company")
public class CourseCompanyController extends BaseController
{
    @Autowired
    private ICourseCompanyService courseCompanyService;

    /**
     * 查询企业课程列表
     */
    @PreAuthorize("@ss.hasPermi('course:company:list')")
    @GetMapping("/list")
    public TableDataInfo list(CourseCompany courseCompany)
    {
        startPage();
        List<CourseCompany> list = courseCompanyService.selectCourseCompanyList(courseCompany);
        return getDataTable(list);
    }

    /**
     * 导出企业课程列表
     */
    @PreAuthorize("@ss.hasPermi('course:company:export')")
    @Log(title = "企业课程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CourseCompany courseCompany)
    {
        List<CourseCompany> list = courseCompanyService.selectCourseCompanyList(courseCompany);
        ExcelUtil<CourseCompany> util = new ExcelUtil<CourseCompany>(CourseCompany.class);
        util.exportExcel(response, list, "企业课程数据");
    }

    /**
     * 获取企业课程详细信息
     */
    @PreAuthorize("@ss.hasPermi('course:company:query')")
    @GetMapping(value = "/{courseId}")
    public AjaxResult getInfo(@PathVariable("courseId") Long courseId)
    {
        return success(courseCompanyService.selectCourseCompanyByCourseId(courseId));
    }

    /**
     * 新增企业课程
     */
    @PreAuthorize("@ss.hasPermi('course:company:add')")
    @Log(title = "企业课程", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CourseCompany courseCompany)
    {
        return toAjax(courseCompanyService.insertCourseCompany(courseCompany));
    }

    /**
     * 修改企业课程
     */
    @PreAuthorize("@ss.hasPermi('course:company:edit')")
    @Log(title = "企业课程", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CourseCompany courseCompany)
    {
        return toAjax(courseCompanyService.updateCourseCompany(courseCompany));
    }

    /**
     * 删除企业课程
     */
    @PreAuthorize("@ss.hasPermi('course:company:remove')")
    @Log(title = "企业课程", businessType = BusinessType.DELETE)
	@DeleteMapping("/{courseIds}")
    public AjaxResult remove(@PathVariable Long[] courseIds)
    {
        return toAjax(courseCompanyService.deleteCourseCompanyByCourseIds(courseIds));
    }
}
