package com.ruoyi.project.course.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 企业课程对象 course_company
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public class CourseCompany extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 课程id */
    private Long courseId;

    /** 课程名称 */
    @Excel(name = "课程名称")
    private String courseName;

    /** 课程标准 */
    @Excel(name = "课程标准")
    private String courseStandard;

    /** 课程学时 */
    @Excel(name = "课程学时")
    private Long courseHours;

    /** 课程学分 */
    @Excel(name = "课程学分")
    private Long courseCredit;

    /** 课程目标 */
    @Excel(name = "课程目标")
    private String courseGoal;

    public void setCourseId(Long courseId) 
    {
        this.courseId = courseId;
    }

    public Long getCourseId() 
    {
        return courseId;
    }

    public void setCourseName(String courseName) 
    {
        this.courseName = courseName;
    }

    public String getCourseName() 
    {
        return courseName;
    }

    public void setCourseStandard(String courseStandard) 
    {
        this.courseStandard = courseStandard;
    }

    public String getCourseStandard() 
    {
        return courseStandard;
    }

    public void setCourseHours(Long courseHours) 
    {
        this.courseHours = courseHours;
    }

    public Long getCourseHours() 
    {
        return courseHours;
    }

    public void setCourseCredit(Long courseCredit) 
    {
        this.courseCredit = courseCredit;
    }

    public Long getCourseCredit() 
    {
        return courseCredit;
    }

    public void setCourseGoal(String courseGoal) 
    {
        this.courseGoal = courseGoal;
    }

    public String getCourseGoal() 
    {
        return courseGoal;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("courseId", getCourseId())
            .append("courseName", getCourseName())
            .append("courseStandard", getCourseStandard())
            .append("courseHours", getCourseHours())
            .append("courseCredit", getCourseCredit())
            .append("courseGoal", getCourseGoal())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
