package com.ruoyi.project.course.mapper;

import java.util.List;
import com.ruoyi.project.course.domain.CourseSchool;

/**
 * 学校课程Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public interface CourseSchoolMapper 
{
    /**
     * 查询学校课程
     * 
     * @param courseId 学校课程主键
     * @return 学校课程
     */
    public CourseSchool selectCourseSchoolByCourseId(Long courseId);

    /**
     * 查询学校课程列表
     * 
     * @param courseSchool 学校课程
     * @return 学校课程集合
     */
    public List<CourseSchool> selectCourseSchoolList(CourseSchool courseSchool);

    /**
     * 新增学校课程
     * 
     * @param courseSchool 学校课程
     * @return 结果
     */
    public int insertCourseSchool(CourseSchool courseSchool);

    /**
     * 修改学校课程
     * 
     * @param courseSchool 学校课程
     * @return 结果
     */
    public int updateCourseSchool(CourseSchool courseSchool);

    /**
     * 删除学校课程
     * 
     * @param courseId 学校课程主键
     * @return 结果
     */
    public int deleteCourseSchoolByCourseId(Long courseId);

    /**
     * 批量删除学校课程
     * 
     * @param courseIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCourseSchoolByCourseIds(Long[] courseIds);
}
