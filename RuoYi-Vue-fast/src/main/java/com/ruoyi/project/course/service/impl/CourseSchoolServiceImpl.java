package com.ruoyi.project.course.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.course.mapper.CourseSchoolMapper;
import com.ruoyi.project.course.domain.CourseSchool;
import com.ruoyi.project.course.service.ICourseSchoolService;

/**
 * 学校课程Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Service
public class CourseSchoolServiceImpl implements ICourseSchoolService 
{
    @Autowired
    private CourseSchoolMapper courseSchoolMapper;

    /**
     * 查询学校课程
     * 
     * @param courseId 学校课程主键
     * @return 学校课程
     */
    @Override
    public CourseSchool selectCourseSchoolByCourseId(Long courseId)
    {
        return courseSchoolMapper.selectCourseSchoolByCourseId(courseId);
    }

    /**
     * 查询学校课程列表
     * 
     * @param courseSchool 学校课程
     * @return 学校课程
     */
    @Override
    public List<CourseSchool> selectCourseSchoolList(CourseSchool courseSchool)
    {
        return courseSchoolMapper.selectCourseSchoolList(courseSchool);
    }

    /**
     * 新增学校课程
     * 
     * @param courseSchool 学校课程
     * @return 结果
     */
    @Override
    public int insertCourseSchool(CourseSchool courseSchool)
    {
        courseSchool.setCreateTime(DateUtils.getNowDate());
        return courseSchoolMapper.insertCourseSchool(courseSchool);
    }

    /**
     * 修改学校课程
     * 
     * @param courseSchool 学校课程
     * @return 结果
     */
    @Override
    public int updateCourseSchool(CourseSchool courseSchool)
    {
        courseSchool.setUpdateTime(DateUtils.getNowDate());
        return courseSchoolMapper.updateCourseSchool(courseSchool);
    }

    /**
     * 批量删除学校课程
     * 
     * @param courseIds 需要删除的学校课程主键
     * @return 结果
     */
    @Override
    public int deleteCourseSchoolByCourseIds(Long[] courseIds)
    {
        return courseSchoolMapper.deleteCourseSchoolByCourseIds(courseIds);
    }

    /**
     * 删除学校课程信息
     * 
     * @param courseId 学校课程主键
     * @return 结果
     */
    @Override
    public int deleteCourseSchoolByCourseId(Long courseId)
    {
        return courseSchoolMapper.deleteCourseSchoolByCourseId(courseId);
    }
}
