package com.ruoyi.project.course.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.course.mapper.CourseCompanyMapper;
import com.ruoyi.project.course.domain.CourseCompany;
import com.ruoyi.project.course.service.ICourseCompanyService;

/**
 * 企业课程Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Service
public class CourseCompanyServiceImpl implements ICourseCompanyService 
{
    @Autowired
    private CourseCompanyMapper courseCompanyMapper;

    /**
     * 查询企业课程
     * 
     * @param courseId 企业课程主键
     * @return 企业课程
     */
    @Override
    public CourseCompany selectCourseCompanyByCourseId(Long courseId)
    {
        return courseCompanyMapper.selectCourseCompanyByCourseId(courseId);
    }

    /**
     * 查询企业课程列表
     * 
     * @param courseCompany 企业课程
     * @return 企业课程
     */
    @Override
    public List<CourseCompany> selectCourseCompanyList(CourseCompany courseCompany)
    {
        return courseCompanyMapper.selectCourseCompanyList(courseCompany);
    }

    /**
     * 新增企业课程
     * 
     * @param courseCompany 企业课程
     * @return 结果
     */
    @Override
    public int insertCourseCompany(CourseCompany courseCompany)
    {
        courseCompany.setCreateTime(DateUtils.getNowDate());
        return courseCompanyMapper.insertCourseCompany(courseCompany);
    }

    /**
     * 修改企业课程
     * 
     * @param courseCompany 企业课程
     * @return 结果
     */
    @Override
    public int updateCourseCompany(CourseCompany courseCompany)
    {
        courseCompany.setUpdateTime(DateUtils.getNowDate());
        return courseCompanyMapper.updateCourseCompany(courseCompany);
    }

    /**
     * 批量删除企业课程
     * 
     * @param courseIds 需要删除的企业课程主键
     * @return 结果
     */
    @Override
    public int deleteCourseCompanyByCourseIds(Long[] courseIds)
    {
        return courseCompanyMapper.deleteCourseCompanyByCourseIds(courseIds);
    }

    /**
     * 删除企业课程信息
     * 
     * @param courseId 企业课程主键
     * @return 结果
     */
    @Override
    public int deleteCourseCompanyByCourseId(Long courseId)
    {
        return courseCompanyMapper.deleteCourseCompanyByCourseId(courseId);
    }
}
