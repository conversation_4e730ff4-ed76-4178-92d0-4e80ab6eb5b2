package com.ruoyi.project.course.service;

import java.util.List;
import com.ruoyi.project.course.domain.CourseSchool;

/**
 * 学校课程Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public interface ICourseSchoolService 
{
    /**
     * 查询学校课程
     * 
     * @param courseId 学校课程主键
     * @return 学校课程
     */
    public CourseSchool selectCourseSchoolByCourseId(Long courseId);

    /**
     * 查询学校课程列表
     * 
     * @param courseSchool 学校课程
     * @return 学校课程集合
     */
    public List<CourseSchool> selectCourseSchoolList(CourseSchool courseSchool);

    /**
     * 新增学校课程
     * 
     * @param courseSchool 学校课程
     * @return 结果
     */
    public int insertCourseSchool(CourseSchool courseSchool);

    /**
     * 修改学校课程
     * 
     * @param courseSchool 学校课程
     * @return 结果
     */
    public int updateCourseSchool(CourseSchool courseSchool);

    /**
     * 批量删除学校课程
     * 
     * @param courseIds 需要删除的学校课程主键集合
     * @return 结果
     */
    public int deleteCourseSchoolByCourseIds(Long[] courseIds);

    /**
     * 删除学校课程信息
     * 
     * @param courseId 学校课程主键
     * @return 结果
     */
    public int deleteCourseSchoolByCourseId(Long courseId);
}
