package com.ruoyi.project.course.mapper;

import java.util.List;
import com.ruoyi.project.course.domain.CourseCompany;

/**
 * 企业课程Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public interface CourseCompanyMapper 
{
    /**
     * 查询企业课程
     * 
     * @param courseId 企业课程主键
     * @return 企业课程
     */
    public CourseCompany selectCourseCompanyByCourseId(Long courseId);

    /**
     * 查询企业课程列表
     * 
     * @param courseCompany 企业课程
     * @return 企业课程集合
     */
    public List<CourseCompany> selectCourseCompanyList(CourseCompany courseCompany);

    /**
     * 新增企业课程
     * 
     * @param courseCompany 企业课程
     * @return 结果
     */
    public int insertCourseCompany(CourseCompany courseCompany);

    /**
     * 修改企业课程
     * 
     * @param courseCompany 企业课程
     * @return 结果
     */
    public int updateCourseCompany(CourseCompany courseCompany);

    /**
     * 删除企业课程
     * 
     * @param courseId 企业课程主键
     * @return 结果
     */
    public int deleteCourseCompanyByCourseId(Long courseId);

    /**
     * 批量删除企业课程
     * 
     * @param courseIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCourseCompanyByCourseIds(Long[] courseIds);
}
