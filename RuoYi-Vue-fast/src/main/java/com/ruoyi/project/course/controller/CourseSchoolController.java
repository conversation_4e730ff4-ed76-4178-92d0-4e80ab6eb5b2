package com.ruoyi.project.course.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.course.domain.CourseSchool;
import com.ruoyi.project.course.service.ICourseSchoolService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 学校课程Controller
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@RestController
@RequestMapping("/course/school")
public class CourseSchoolController extends BaseController
{
    @Autowired
    private ICourseSchoolService courseSchoolService;

    /**
     * 查询学校课程列表
     */
    @PreAuthorize("@ss.hasPermi('course:school:list')")
    @GetMapping("/list")
    public TableDataInfo list(CourseSchool courseSchool)
    {
        startPage();
        List<CourseSchool> list = courseSchoolService.selectCourseSchoolList(courseSchool);
        return getDataTable(list);
    }

    /**
     * 导出学校课程列表
     */
    @PreAuthorize("@ss.hasPermi('course:school:export')")
    @Log(title = "学校课程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CourseSchool courseSchool)
    {
        List<CourseSchool> list = courseSchoolService.selectCourseSchoolList(courseSchool);
        ExcelUtil<CourseSchool> util = new ExcelUtil<CourseSchool>(CourseSchool.class);
        util.exportExcel(response, list, "学校课程数据");
    }

    /**
     * 获取学校课程详细信息
     */
    @PreAuthorize("@ss.hasPermi('course:school:query')")
    @GetMapping(value = "/{courseId}")
    public AjaxResult getInfo(@PathVariable("courseId") Long courseId)
    {
        return success(courseSchoolService.selectCourseSchoolByCourseId(courseId));
    }

    /**
     * 新增学校课程
     */
    @PreAuthorize("@ss.hasPermi('course:school:add')")
    @Log(title = "学校课程", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CourseSchool courseSchool)
    {
        return toAjax(courseSchoolService.insertCourseSchool(courseSchool));
    }

    /**
     * 修改学校课程
     */
    @PreAuthorize("@ss.hasPermi('course:school:edit')")
    @Log(title = "学校课程", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CourseSchool courseSchool)
    {
        return toAjax(courseSchoolService.updateCourseSchool(courseSchool));
    }

    /**
     * 删除学校课程
     */
    @PreAuthorize("@ss.hasPermi('course:school:remove')")
    @Log(title = "学校课程", businessType = BusinessType.DELETE)
	@DeleteMapping("/{courseIds}")
    public AjaxResult remove(@PathVariable Long[] courseIds)
    {
        return toAjax(courseSchoolService.deleteCourseSchoolByCourseIds(courseIds));
    }
}
