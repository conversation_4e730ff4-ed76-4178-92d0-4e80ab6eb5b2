package com.ruoyi.project.profile.controller;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.profile.domain.PositionCompetencyStats;
import com.ruoyi.project.profile.service.IGroupPortraitService;

/**
 * 群体画像Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/profile/group")
public class GroupPortraitController extends BaseController
{
    @Autowired
    private IGroupPortraitService groupPortraitService;

    /**
     * 获取岗位能力达标统计数据
     */
    @PreAuthorize("@ss.hasPermi('student:info:list')")
    @GetMapping("/positionCompetencyStats")
    public AjaxResult getPositionCompetencyStats()
    {
        List<PositionCompetencyStats> stats = groupPortraitService.getPositionCompetencyStats();
        return success(stats);
    }
}
