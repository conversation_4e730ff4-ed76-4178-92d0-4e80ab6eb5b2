package com.ruoyi.project.profile.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.project.profile.domain.PositionCompetencyStats;
import com.ruoyi.project.profile.service.IGroupPortraitService;
import com.ruoyi.project.student.domain.StudentInfo;
import com.ruoyi.project.student.domain.StudentExamScore;
import com.ruoyi.project.student.service.IStudentInfoService;
import com.ruoyi.project.student.service.IStudentExamScoreService;
import com.ruoyi.project.training.domain.PostModel;
import com.ruoyi.project.training.domain.PostModelConfig;
import com.ruoyi.project.training.domain.PostAbilityConfig;
import com.ruoyi.project.training.domain.PostCourseConfig;
import com.ruoyi.project.training.service.IPostModelService;
import com.ruoyi.project.training.service.IPostModelConfigService;

/**
 * 群体画像Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class GroupPortraitServiceImpl implements IGroupPortraitService 
{
    private static final Logger log = LoggerFactory.getLogger(GroupPortraitServiceImpl.class);

    @Autowired
    private IStudentInfoService studentInfoService;

    @Autowired
    private IStudentExamScoreService studentExamScoreService;

    @Autowired
    private IPostModelService postModelService;

    @Autowired
    private IPostModelConfigService postModelConfigService;

    /**
     * 获取岗位能力达标统计数据
     * 
     * @return 岗位能力达标统计列表
     */
    @Override
    public List<PositionCompetencyStats> getPositionCompetencyStats()
    {
        List<PositionCompetencyStats> result = new ArrayList<>();
        
        try {
            // 1. 获取所有学生信息
            List<StudentInfo> allStudents = studentInfoService.selectStudentInfoList(new StudentInfo());
            if (allStudents == null || allStudents.isEmpty()) {
                log.warn("未找到学生信息");
                return result;
            }

            // 2. 获取所有岗位信息
            List<PostModel> allPosts = postModelService.selectPostModelList(new PostModel());
            if (allPosts == null || allPosts.isEmpty()) {
                log.warn("未找到岗位信息");
                return result;
            }

            // 3. 按岗位分组学生
            Map<String, List<StudentInfo>> postStudentMap = new HashMap<>();
            for (StudentInfo student : allStudents) {
                String postName = getStudentPostName(student);
                if (StringUtils.isNotEmpty(postName)) {
                    postStudentMap.computeIfAbsent(postName, k -> new ArrayList<>()).add(student);
                }
            }

            // 4. 计算每个岗位的能力达标率
            for (Map.Entry<String, List<StudentInfo>> entry : postStudentMap.entrySet()) {
                String postName = entry.getKey();
                List<StudentInfo> postStudents = entry.getValue();
                
                // 找到对应的岗位配置
                PostModel post = findPostByName(allPosts, postName);
                if (post == null) {
                    log.warn("未找到岗位配置: {}", postName);
                    continue;
                }

                PositionCompetencyStats stats = calculatePostCompetencyStats(postName, postStudents, post);
                if (stats != null) {
                    result.add(stats);
                }
            }

            // 5. 按达标率排序
            result.sort((a, b) -> b.getCompetencyRate().compareTo(a.getCompetencyRate()));

        } catch (Exception e) {
            log.error("获取岗位能力达标统计数据失败", e);
        }

        return result;
    }

    /**
     * 获取学生的岗位名称
     */
    private String getStudentPostName(StudentInfo student)
    {
        // 优先使用合同中的岗位名称，其次使用目标岗位
        if (StringUtils.isNotEmpty(student.getPostName())) {
            return student.getPostName().trim();
        }
        if (StringUtils.isNotEmpty(student.getTargetPosition())) {
            return student.getTargetPosition().trim();
        }
        return null;
    }

    /**
     * 根据岗位名称查找岗位
     */
    private PostModel findPostByName(List<PostModel> posts, String postName)
    {
        for (PostModel post : posts) {
            if (postName.equals(post.getPostName())) {
                return post;
            }
        }
        return null;
    }

    /**
     * 计算岗位能力达标统计
     */
    private PositionCompetencyStats calculatePostCompetencyStats(String postName, List<StudentInfo> students, PostModel post)
    {
        try {
            // 获取岗位模型配置
            PostModelConfig config = postModelConfigService.selectPostModelConfigByPostId(post.getPostId());
            if (config == null || config.getAbilityConfigs() == null || config.getAbilityConfigs().isEmpty()) {
                log.warn("岗位 {} 未配置职业能力", postName);
                return new PositionCompetencyStats(postName, students.size(), 0, 0);
            }

            int totalStudents = students.size();
            int qualifiedStudents = 0;

            // 计算每个学生的能力达标情况
            for (StudentInfo student : students) {
                if (isStudentQualified(student, config)) {
                    qualifiedStudents++;
                }
            }

            // 计算达标率
            int competencyRate = totalStudents > 0 ? Math.round((float) qualifiedStudents / totalStudents * 100) : 0;

            return new PositionCompetencyStats(postName, totalStudents, qualifiedStudents, competencyRate);

        } catch (Exception e) {
            log.error("计算岗位 {} 能力达标统计失败", postName, e);
            return new PositionCompetencyStats(postName, students.size(), 0, 0);
        }
    }

    /**
     * 判断学生是否达标
     */
    private boolean isStudentQualified(StudentInfo student, PostModelConfig config)
    {
        try {
            // 获取学生的考试成绩
            List<StudentExamScore> studentScores = studentExamScoreService.selectStudentExamScoreListByStudentId(student.getStudentId());
            if (studentScores == null) {
                studentScores = new ArrayList<>();
            }

            // 计算未达标的职业能力数量
            int notAchievedCount = 0;

            for (PostAbilityConfig abilityConfig : config.getAbilityConfigs()) {
                double abilityScore = calculateAbilityScore(abilityConfig, studentScores);
                double threshold = abilityConfig.getThreshold() != null ? abilityConfig.getThreshold() : 60.0;

                // 如果职业能力分值 < 阈值，则该能力未达标
                if (abilityScore < threshold) {
                    notAchievedCount++;
                }
            }

            // 获取学生的岗位能力达标阈值（默认为2）
            int studentThreshold = student.getPositionCompetencyThreshold() != null ?
                student.getPositionCompetencyThreshold() : 2;

            // 如果未达标能力数量 < 学生的岗位能力达标阈值，则该学生达标
            return notAchievedCount < studentThreshold;

        } catch (Exception e) {
            log.error("判断学生 {} 是否达标失败", student.getStudentName(), e);
            return false;
        }
    }

    /**
     * 计算职业能力分值
     */
    private double calculateAbilityScore(PostAbilityConfig abilityConfig, List<StudentExamScore> studentScores)
    {
        if (abilityConfig.getCourseConfigs() == null || abilityConfig.getCourseConfigs().isEmpty()) {
            return 0.0;
        }

        double totalWeightedScore = 0.0;
        double totalWeight = 0.0;

        // 遍历该职业能力关联的所有课程
        for (PostCourseConfig courseConfig : abilityConfig.getCourseConfigs()) {
            // 找到学生该课程的所有成绩
            List<StudentExamScore> courseScores = new ArrayList<>();
            for (StudentExamScore score : studentScores) {
                if (courseConfig.getCourseName().equals(score.getCourseName())) {
                    courseScores.add(score);
                }
            }

            if (!courseScores.isEmpty()) {
                // 计算该课程的平均成绩
                double avgScore = calculateCourseAverage(courseScores);

                // 按权重累加
                double weight = courseConfig.getWeight() != null ? courseConfig.getWeight() / 100.0 : 1.0;
                totalWeightedScore += avgScore * weight;
                totalWeight += weight;
            }
        }

        // 返回加权平均分
        return totalWeight > 0 ? totalWeightedScore / totalWeight : 0.0;
    }

    /**
     * 计算课程平均成绩
     */
    private double calculateCourseAverage(List<StudentExamScore> courseScores)
    {
        if (courseScores == null || courseScores.isEmpty()) {
            return 0.0;
        }

        double total = 0.0;
        for (StudentExamScore score : courseScores) {
            total += score.getScore() != null ? score.getScore().doubleValue() : 0.0;
        }

        return total / courseScores.size();
    }
}
