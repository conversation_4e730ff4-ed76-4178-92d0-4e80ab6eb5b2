package com.ruoyi.project.training.mapper;

import java.util.List;
import com.ruoyi.project.training.domain.EvaluationRecord;

/**
 * 评价记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface EvaluationRecordMapper 
{
    /**
     * 查询评价记录
     * 
     * @param recordId 评价记录主键
     * @return 评价记录
     */
    public EvaluationRecord selectEvaluationRecordByRecordId(Long recordId);

    /**
     * 查询评价记录列表
     * 
     * @param evaluationRecord 评价记录
     * @return 评价记录集合
     */
    public List<EvaluationRecord> selectEvaluationRecordList(EvaluationRecord evaluationRecord);

    /**
     * 根据学生ID查询评价记录列表
     * 
     * @param studentId 学生ID
     * @return 评价记录集合
     */
    public List<EvaluationRecord> selectEvaluationRecordListByStudentId(Long studentId);

    /**
     * 新增评价记录
     * 
     * @param evaluationRecord 评价记录
     * @return 结果
     */
    public int insertEvaluationRecord(EvaluationRecord evaluationRecord);

    /**
     * 修改评价记录
     * 
     * @param evaluationRecord 评价记录
     * @return 结果
     */
    public int updateEvaluationRecord(EvaluationRecord evaluationRecord);

    /**
     * 删除评价记录
     * 
     * @param recordId 评价记录主键
     * @return 结果
     */
    public int deleteEvaluationRecordByRecordId(Long recordId);

    /**
     * 批量删除评价记录
     * 
     * @param recordIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEvaluationRecordByRecordIds(Long[] recordIds);
}
