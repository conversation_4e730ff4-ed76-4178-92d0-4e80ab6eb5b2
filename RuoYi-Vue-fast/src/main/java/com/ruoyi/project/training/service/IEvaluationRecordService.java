package com.ruoyi.project.training.service;

import java.util.List;
import com.ruoyi.project.training.domain.EvaluationRecord;
import com.ruoyi.project.training.domain.EvaluationRecordAnswer;

/**
 * 评价记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IEvaluationRecordService 
{
    /**
     * 查询评价记录
     * 
     * @param recordId 评价记录主键
     * @return 评价记录
     */
    public EvaluationRecord selectEvaluationRecordByRecordId(Long recordId);

    /**
     * 查询评价记录列表
     * 
     * @param evaluationRecord 评价记录
     * @return 评价记录集合
     */
    public List<EvaluationRecord> selectEvaluationRecordList(EvaluationRecord evaluationRecord);

    /**
     * 根据学生ID查询评价记录列表
     * 
     * @param studentId 学生ID
     * @return 评价记录集合
     */
    public List<EvaluationRecord> selectEvaluationRecordListByStudentId(Long studentId);

    /**
     * 新增评价记录
     * 
     * @param evaluationRecord 评价记录
     * @return 结果
     */
    public int insertEvaluationRecord(EvaluationRecord evaluationRecord);

    /**
     * 修改评价记录
     * 
     * @param evaluationRecord 评价记录
     * @return 结果
     */
    public int updateEvaluationRecord(EvaluationRecord evaluationRecord);

    /**
     * 批量删除评价记录
     * 
     * @param recordIds 需要删除的评价记录主键集合
     * @return 结果
     */
    public int deleteEvaluationRecordByRecordIds(Long[] recordIds);

    /**
     * 删除评价记录信息
     * 
     * @param recordId 评价记录主键
     * @return 结果
     */
    public int deleteEvaluationRecordByRecordId(Long recordId);

    /**
     * 创建评价记录并选择题目
     * 
     * @param studentId 学生ID
     * @param bankId 题库ID
     * @param questionIds 选择的题目ID列表
     * @return 评价记录
     */
    public EvaluationRecord createEvaluationRecord(Long studentId, Long bankId, Long[] questionIds);

    /**
     * 提交答题记录
     * 
     * @param recordId 评价记录ID
     * @param answerList 答题记录列表
     * @return 结果
     */
    public int submitAnswers(Long recordId, List<EvaluationRecordAnswer> answerList);

    /**
     * 完成评价记录
     * 
     * @param recordId 评价记录ID
     * @return 结果
     */
    public int completeEvaluationRecord(Long recordId);

    /**
     * 获取评价记录的答题详情
     * 
     * @param recordId 评价记录ID
     * @return 答题记录列表
     */
    public List<EvaluationRecordAnswer> getEvaluationRecordAnswers(Long recordId);
}
