package com.ruoyi.project.training.service.impl;

import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.text.SimpleDateFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.project.training.mapper.EvaluationRecordMapper;
import com.ruoyi.project.training.mapper.EvaluationRecordAnswerMapper;
import com.ruoyi.project.training.mapper.EvaluationQuestionMapper;
import com.ruoyi.project.training.mapper.EvaluationQuestionBankMapper;
import com.ruoyi.project.training.domain.EvaluationRecord;
import com.ruoyi.project.training.domain.EvaluationRecordAnswer;
import com.ruoyi.project.training.domain.EvaluationQuestion;
import com.ruoyi.project.training.domain.EvaluationQuestionBank;
import com.ruoyi.project.training.service.IEvaluationRecordService;

/**
 * 评价记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class EvaluationRecordServiceImpl implements IEvaluationRecordService 
{
    @Autowired
    private EvaluationRecordMapper evaluationRecordMapper;

    @Autowired
    private EvaluationRecordAnswerMapper evaluationRecordAnswerMapper;

    @Autowired
    private EvaluationQuestionMapper evaluationQuestionMapper;

    @Autowired
    private EvaluationQuestionBankMapper evaluationQuestionBankMapper;

    /**
     * 查询评价记录
     * 
     * @param recordId 评价记录主键
     * @return 评价记录
     */
    @Override
    public EvaluationRecord selectEvaluationRecordByRecordId(Long recordId)
    {
        return evaluationRecordMapper.selectEvaluationRecordByRecordId(recordId);
    }

    /**
     * 查询评价记录列表
     * 
     * @param evaluationRecord 评价记录
     * @return 评价记录
     */
    @Override
    public List<EvaluationRecord> selectEvaluationRecordList(EvaluationRecord evaluationRecord)
    {
        return evaluationRecordMapper.selectEvaluationRecordList(evaluationRecord);
    }

    /**
     * 根据学生ID查询评价记录列表
     * 
     * @param studentId 学生ID
     * @return 评价记录集合
     */
    @Override
    public List<EvaluationRecord> selectEvaluationRecordListByStudentId(Long studentId)
    {
        return evaluationRecordMapper.selectEvaluationRecordListByStudentId(studentId);
    }

    /**
     * 新增评价记录
     * 
     * @param evaluationRecord 评价记录
     * @return 结果
     */
    @Override
    public int insertEvaluationRecord(EvaluationRecord evaluationRecord)
    {
        evaluationRecord.setCreateBy(SecurityUtils.getUsername());
        return evaluationRecordMapper.insertEvaluationRecord(evaluationRecord);
    }

    /**
     * 修改评价记录
     * 
     * @param evaluationRecord 评价记录
     * @return 结果
     */
    @Override
    public int updateEvaluationRecord(EvaluationRecord evaluationRecord)
    {
        evaluationRecord.setUpdateBy(SecurityUtils.getUsername());
        return evaluationRecordMapper.updateEvaluationRecord(evaluationRecord);
    }

    /**
     * 批量删除评价记录
     * 
     * @param recordIds 需要删除的评价记录主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteEvaluationRecordByRecordIds(Long[] recordIds)
    {
        // 先删除相关的答题记录
        for (Long recordId : recordIds) {
            evaluationRecordAnswerMapper.deleteEvaluationRecordAnswerByRecordId(recordId);
        }
        return evaluationRecordMapper.deleteEvaluationRecordByRecordIds(recordIds);
    }

    /**
     * 删除评价记录信息
     * 
     * @param recordId 评价记录主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteEvaluationRecordByRecordId(Long recordId)
    {
        // 先删除相关的答题记录
        evaluationRecordAnswerMapper.deleteEvaluationRecordAnswerByRecordId(recordId);
        return evaluationRecordMapper.deleteEvaluationRecordByRecordId(recordId);
    }

    /**
     * 创建评价记录并选择题目
     * 
     * @param studentId 学生ID
     * @param bankId 题库ID
     * @param questionIds 选择的题目ID列表
     * @return 评价记录
     */
    @Override
    @Transactional
    public EvaluationRecord createEvaluationRecord(Long studentId, Long bankId, Long[] questionIds)
    {
        // 获取题库信息
        EvaluationQuestionBank bank = evaluationQuestionBankMapper.selectEvaluationQuestionBankByBankId(bankId);
        if (bank == null) {
            throw new RuntimeException("题库不存在");
        }

        // 生成评价记录名称（当前时间）
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm");
        String recordName = sdf.format(new Date());

        // 创建评价记录
        EvaluationRecord record = new EvaluationRecord();
        record.setRecordName(recordName);
        record.setStudentId(studentId);
        record.setBankId(bankId);
        record.setBankName(bank.getBankName());
        record.setTotalQuestions(questionIds.length);
        record.setAnsweredQuestions(0);
        record.setStatus("0"); // 进行中
        record.setStartTime(new Date());
        record.setCreateBy(SecurityUtils.getUsername());

        evaluationRecordMapper.insertEvaluationRecord(record);

        // 为每个选择的题目创建答题记录
        List<EvaluationRecordAnswer> answerList = new ArrayList<>();
        for (Long questionId : questionIds) {
            EvaluationQuestion question = evaluationQuestionMapper.selectEvaluationQuestionByQuestionId(questionId);
            if (question != null) {
                EvaluationRecordAnswer answer = new EvaluationRecordAnswer();
                answer.setRecordId(record.getRecordId());
                answer.setQuestionId(questionId);
                answer.setQuestionStem(question.getQuestionStem());
                answer.setQuestionType(question.getQuestionType());
                answer.setOptions(question.getOptions());
                answer.setCreateBy(SecurityUtils.getUsername());
                answerList.add(answer);
            }
        }

        if (!answerList.isEmpty()) {
            evaluationRecordAnswerMapper.batchInsertEvaluationRecordAnswer(answerList);
        }

        return record;
    }

    /**
     * 提交答题记录
     *
     * @param recordId 评价记录ID
     * @param answerList 答题记录列表
     * @return 结果
     */
    @Override
    @Transactional
    public int submitAnswers(Long recordId, List<EvaluationRecordAnswer> answerList)
    {
        int result = 0;
        Date answerTime = new Date();

        for (EvaluationRecordAnswer answer : answerList) {
            answer.setRecordId(recordId);
            answer.setAnswerTime(answerTime);
            answer.setUpdateBy(SecurityUtils.getUsername());
            // 使用新的方法根据recordId和questionId更新答题记录
            result += evaluationRecordAnswerMapper.updateEvaluationRecordAnswerByRecordAndQuestion(answer);
        }

        // 更新已答题目数
        EvaluationRecord record = evaluationRecordMapper.selectEvaluationRecordByRecordId(recordId);
        if (record != null) {
            record.setAnsweredQuestions(answerList.size());
            record.setUpdateBy(SecurityUtils.getUsername());
            evaluationRecordMapper.updateEvaluationRecord(record);
        }

        return result;
    }

    /**
     * 完成评价记录
     * 
     * @param recordId 评价记录ID
     * @return 结果
     */
    @Override
    public int completeEvaluationRecord(Long recordId)
    {
        EvaluationRecord record = new EvaluationRecord();
        record.setRecordId(recordId);
        record.setStatus("1"); // 已完成
        record.setCompleteTime(new Date());
        record.setUpdateBy(SecurityUtils.getUsername());
        return evaluationRecordMapper.updateEvaluationRecord(record);
    }

    /**
     * 获取评价记录的答题详情
     * 
     * @param recordId 评价记录ID
     * @return 答题记录列表
     */
    @Override
    public List<EvaluationRecordAnswer> getEvaluationRecordAnswers(Long recordId)
    {
        return evaluationRecordAnswerMapper.selectEvaluationRecordAnswerListByRecordId(recordId);
    }
}
