package com.ruoyi.project.training.mapper;

import java.util.List;
import com.ruoyi.project.training.domain.EvaluationRecordAnswer;

/**
 * 评价记录答题Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface EvaluationRecordAnswerMapper 
{
    /**
     * 查询评价记录答题
     * 
     * @param answerId 评价记录答题主键
     * @return 评价记录答题
     */
    public EvaluationRecordAnswer selectEvaluationRecordAnswerByAnswerId(Long answerId);

    /**
     * 查询评价记录答题列表
     * 
     * @param evaluationRecordAnswer 评价记录答题
     * @return 评价记录答题集合
     */
    public List<EvaluationRecordAnswer> selectEvaluationRecordAnswerList(EvaluationRecordAnswer evaluationRecordAnswer);

    /**
     * 根据评价记录ID查询答题列表
     * 
     * @param recordId 评价记录ID
     * @return 评价记录答题集合
     */
    public List<EvaluationRecordAnswer> selectEvaluationRecordAnswerListByRecordId(Long recordId);

    /**
     * 新增评价记录答题
     * 
     * @param evaluationRecordAnswer 评价记录答题
     * @return 结果
     */
    public int insertEvaluationRecordAnswer(EvaluationRecordAnswer evaluationRecordAnswer);

    /**
     * 批量新增评价记录答题
     * 
     * @param evaluationRecordAnswerList 评价记录答题列表
     * @return 结果
     */
    public int batchInsertEvaluationRecordAnswer(List<EvaluationRecordAnswer> evaluationRecordAnswerList);

    /**
     * 修改评价记录答题
     *
     * @param evaluationRecordAnswer 评价记录答题
     * @return 结果
     */
    public int updateEvaluationRecordAnswer(EvaluationRecordAnswer evaluationRecordAnswer);

    /**
     * 根据记录ID和题目ID修改答题记录
     *
     * @param evaluationRecordAnswer 评价记录答题
     * @return 结果
     */
    public int updateEvaluationRecordAnswerByRecordAndQuestion(EvaluationRecordAnswer evaluationRecordAnswer);

    /**
     * 删除评价记录答题
     * 
     * @param answerId 评价记录答题主键
     * @return 结果
     */
    public int deleteEvaluationRecordAnswerByAnswerId(Long answerId);

    /**
     * 批量删除评价记录答题
     * 
     * @param answerIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEvaluationRecordAnswerByAnswerIds(Long[] answerIds);

    /**
     * 根据评价记录ID删除答题记录
     * 
     * @param recordId 评价记录ID
     * @return 结果
     */
    public int deleteEvaluationRecordAnswerByRecordId(Long recordId);
}
