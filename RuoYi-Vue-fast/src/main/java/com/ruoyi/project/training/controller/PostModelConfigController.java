package com.ruoyi.project.training.controller;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.training.domain.PostModelConfig;
import com.ruoyi.project.training.service.IPostModelConfigService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.common.utils.poi.ExcelUtil;

/**
 * 岗位模型配置Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/training/post-config")
public class PostModelConfigController extends BaseController
{
    @Autowired
    private IPostModelConfigService postModelConfigService;

    /**
     * 查询岗位模型配置列表
     */
    @PreAuthorize("@ss.hasPermit('training:post:list')")
    @GetMapping("/list")
    public TableDataInfo list(PostModelConfig postModelConfig)
    {
        startPage();
        List<PostModelConfig> list = postModelConfigService.selectPostModelConfigList(postModelConfig);
        return getDataTable(list);
    }

    /**
     * 获取岗位模型配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('training:post:list')")
    @GetMapping(value = "/{postId}")
    public AjaxResult getInfo(@PathVariable("postId") Long postId)
    {
        return success(postModelConfigService.selectPostModelConfigByPostId(postId));
    }

    /**
     * 新增岗位模型配置
     */
    @PreAuthorize("@ss.hasPermi('training:post:list')")
    @Log(title = "岗位模型配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PostModelConfig postModelConfig)
    {
        return toAjax(postModelConfigService.insertPostModelConfig(postModelConfig));
    }

    /**
     * 修改岗位模型配置
     */
    @PreAuthorize("@ss.hasPermi('training:post:list')")
    @Log(title = "岗位模型配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PostModelConfig postModelConfig)
    {
        return toAjax(postModelConfigService.updatePostModelConfig(postModelConfig));
    }

    /**
     * 保存岗位模型配置（包含职业能力和课程配置）
     */
    @PreAuthorize("@ss.hasPermi('training:post:list')")
    @Log(title = "岗位模型配置", businessType = BusinessType.UPDATE)
    @PostMapping("/save")
    public AjaxResult save(@RequestBody PostModelConfig postModelConfig)
    {
        return toAjax(postModelConfigService.savePostModelConfig(postModelConfig));
    }

    /**
     * 删除岗位模型配置
     */
    @PreAuthorize("@ss.hasPermi('training:post:list')")
    @Log(title = "岗位模型配置", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public AjaxResult remove(@RequestParam String postIds)
    {
        String[] ids = postIds.split(",");
        Long[] postIdArray = new Long[ids.length];
        for (int i = 0; i < ids.length; i++) {
            postIdArray[i] = Long.parseLong(ids[i]);
        }
        return toAjax(postModelConfigService.deletePostModelConfigByPostIds(postIdArray));
    }


} 