package com.ruoyi.project.training.domain;

import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 评价记录对象 evaluation_record
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class EvaluationRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 评价记录ID */
    private Long recordId;

    /** 评价记录名称 */
    @Excel(name = "评价记录名称")
    @NotBlank(message = "评价记录名称不能为空")
    @Size(min = 0, max = 100, message = "评价记录名称长度不能超过100个字符")
    private String recordName;

    /** 学生ID */
    @Excel(name = "学生ID")
    @NotNull(message = "学生ID不能为空")
    private Long studentId;

    /** 题库ID */
    @Excel(name = "题库ID")
    @NotNull(message = "题库ID不能为空")
    private Long bankId;

    /** 题库名称 */
    @Excel(name = "题库名称")
    @NotBlank(message = "题库名称不能为空")
    @Size(min = 0, max = 60, message = "题库名称长度不能超过60个字符")
    private String bankName;

    /** 总题目数 */
    @Excel(name = "总题目数")
    private Integer totalQuestions;

    /** 已答题目数 */
    @Excel(name = "已答题目数")
    private Integer answeredQuestions;

    /** 状态（0进行中 1已完成） */
    @Excel(name = "状态", readConverterExp = "0=进行中,1=已完成")
    private String status;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    /** 学生姓名（关联查询） */
    private String studentName;

    /** 答题记录列表 */
    private List<EvaluationRecordAnswer> answerList;

    public void setRecordId(Long recordId) 
    {
        this.recordId = recordId;
    }

    public Long getRecordId() 
    {
        return recordId;
    }

    public void setRecordName(String recordName) 
    {
        this.recordName = recordName;
    }

    public String getRecordName() 
    {
        return recordName;
    }

    public void setStudentId(Long studentId) 
    {
        this.studentId = studentId;
    }

    public Long getStudentId() 
    {
        return studentId;
    }

    public void setBankId(Long bankId) 
    {
        this.bankId = bankId;
    }

    public Long getBankId() 
    {
        return bankId;
    }

    public void setBankName(String bankName) 
    {
        this.bankName = bankName;
    }

    public String getBankName() 
    {
        return bankName;
    }

    public void setTotalQuestions(Integer totalQuestions) 
    {
        this.totalQuestions = totalQuestions;
    }

    public Integer getTotalQuestions() 
    {
        return totalQuestions;
    }

    public void setAnsweredQuestions(Integer answeredQuestions) 
    {
        this.answeredQuestions = answeredQuestions;
    }

    public Integer getAnsweredQuestions() 
    {
        return answeredQuestions;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }

    public void setCompleteTime(Date completeTime) 
    {
        this.completeTime = completeTime;
    }

    public Date getCompleteTime() 
    {
        return completeTime;
    }

    public void setStudentName(String studentName) 
    {
        this.studentName = studentName;
    }

    public String getStudentName() 
    {
        return studentName;
    }

    public void setAnswerList(List<EvaluationRecordAnswer> answerList) 
    {
        this.answerList = answerList;
    }

    public List<EvaluationRecordAnswer> getAnswerList() 
    {
        return answerList;
    }

    @Override
    public String toString() {
        return new StringBuilder()
            .append("EvaluationRecord{")
            .append("recordId=").append(recordId)
            .append(", recordName='").append(recordName).append('\'')
            .append(", studentId=").append(studentId)
            .append(", bankId=").append(bankId)
            .append(", bankName='").append(bankName).append('\'')
            .append(", totalQuestions=").append(totalQuestions)
            .append(", answeredQuestions=").append(answeredQuestions)
            .append(", status='").append(status).append('\'')
            .append(", startTime=").append(startTime)
            .append(", completeTime=").append(completeTime)
            .append(", studentName='").append(studentName).append('\'')
            .append('}')
            .toString();
    }
}
