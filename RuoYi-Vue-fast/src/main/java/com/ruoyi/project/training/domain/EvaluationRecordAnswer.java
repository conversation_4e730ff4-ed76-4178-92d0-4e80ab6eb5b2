package com.ruoyi.project.training.domain;

import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 评价记录答题对象 evaluation_record_answer
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class EvaluationRecordAnswer extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 答题记录ID */
    private Long answerId;

    /** 评价记录ID */
    @Excel(name = "评价记录ID")
    @NotNull(message = "评价记录ID不能为空")
    private Long recordId;

    /** 题目ID */
    @Excel(name = "题目ID")
    @NotNull(message = "题目ID不能为空")
    private Long questionId;

    /** 题干 */
    @Excel(name = "题干")
    @NotBlank(message = "题干不能为空")
    @Size(min = 0, max = 500, message = "题干长度不能超过500个字符")
    private String questionStem;

    /** 题目类型（0单选题 1多选题） */
    @Excel(name = "题目类型", readConverterExp = "0=单选题,1=多选题")
    @NotBlank(message = "题目类型不能为空")
    private String questionType;

    /** 选项（JSON格式） */
    @Excel(name = "选项")
    @NotBlank(message = "选项不能为空")
    private String options;

    /** 用户答案 */
    @Excel(name = "用户答案")
    @Size(min = 0, max = 500, message = "用户答案长度不能超过500个字符")
    private String userAnswer;

    /** 答题时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "答题时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date answerTime;

    public void setAnswerId(Long answerId) 
    {
        this.answerId = answerId;
    }

    public Long getAnswerId() 
    {
        return answerId;
    }

    public void setRecordId(Long recordId) 
    {
        this.recordId = recordId;
    }

    public Long getRecordId() 
    {
        return recordId;
    }

    public void setQuestionId(Long questionId) 
    {
        this.questionId = questionId;
    }

    public Long getQuestionId() 
    {
        return questionId;
    }

    public void setQuestionStem(String questionStem) 
    {
        this.questionStem = questionStem;
    }

    public String getQuestionStem() 
    {
        return questionStem;
    }

    public void setQuestionType(String questionType) 
    {
        this.questionType = questionType;
    }

    public String getQuestionType() 
    {
        return questionType;
    }

    public void setOptions(String options) 
    {
        this.options = options;
    }

    public String getOptions() 
    {
        return options;
    }

    public void setUserAnswer(String userAnswer) 
    {
        this.userAnswer = userAnswer;
    }

    public String getUserAnswer() 
    {
        return userAnswer;
    }

    public void setAnswerTime(Date answerTime) 
    {
        this.answerTime = answerTime;
    }

    public Date getAnswerTime() 
    {
        return answerTime;
    }

    @Override
    public String toString() {
        return new StringBuilder()
            .append("EvaluationRecordAnswer{")
            .append("answerId=").append(answerId)
            .append(", recordId=").append(recordId)
            .append(", questionId=").append(questionId)
            .append(", questionStem='").append(questionStem).append('\'')
            .append(", questionType='").append(questionType).append('\'')
            .append(", options='").append(options).append('\'')
            .append(", userAnswer='").append(userAnswer).append('\'')
            .append(", answerTime=").append(answerTime)
            .append('}')
            .toString();
    }
}
