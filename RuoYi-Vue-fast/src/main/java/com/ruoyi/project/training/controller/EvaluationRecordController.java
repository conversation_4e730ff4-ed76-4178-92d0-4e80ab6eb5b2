package com.ruoyi.project.training.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.training.domain.EvaluationRecord;
import com.ruoyi.project.training.domain.EvaluationRecordAnswer;
import com.ruoyi.project.training.service.IEvaluationRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 评价记录Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/training/record")
public class EvaluationRecordController extends BaseController
{
    @Autowired
    private IEvaluationRecordService evaluationRecordService;

    /**
     * 查询评价记录列表
     */
    @PreAuthorize("@ss.hasPermi('training:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(EvaluationRecord evaluationRecord)
    {
        startPage();
        List<EvaluationRecord> list = evaluationRecordService.selectEvaluationRecordList(evaluationRecord);
        return getDataTable(list);
    }

    /**
     * 根据学生ID查询评价记录列表
     */
    @GetMapping("/listByStudent/{studentId}")
    public AjaxResult listByStudent(@PathVariable("studentId") Long studentId)
    {
        List<EvaluationRecord> list = evaluationRecordService.selectEvaluationRecordListByStudentId(studentId);
        return success(list);
    }

    /**
     * 导出评价记录列表
     */
    @PreAuthorize("@ss.hasPermi('training:record:list')")
    @Log(title = "评价记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EvaluationRecord evaluationRecord)
    {
        List<EvaluationRecord> list = evaluationRecordService.selectEvaluationRecordList(evaluationRecord);
        ExcelUtil<EvaluationRecord> util = new ExcelUtil<EvaluationRecord>(EvaluationRecord.class);
        util.exportExcel(response, list, "评价记录数据");
    }

    /**
     * 获取评价记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('training:record:list')")
    @GetMapping(value = "/{recordId}")
    public AjaxResult getInfo(@PathVariable("recordId") Long recordId)
    {
        return success(evaluationRecordService.selectEvaluationRecordByRecordId(recordId));
    }

    /**
     * 获取评价记录的答题详情
     */
    @GetMapping(value = "/answers/{recordId}")
    public AjaxResult getAnswers(@PathVariable("recordId") Long recordId)
    {
        List<EvaluationRecordAnswer> answers = evaluationRecordService.getEvaluationRecordAnswers(recordId);
        return success(answers);
    }

    /**
     * 新增评价记录
     */
    @PreAuthorize("@ss.hasPermi('training:record:list')")
    @Log(title = "评价记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EvaluationRecord evaluationRecord)
    {
        return toAjax(evaluationRecordService.insertEvaluationRecord(evaluationRecord));
    }

    /**
     * 创建评价记录并选择题目
     */
    @Log(title = "创建评价记录", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public AjaxResult createRecord(@RequestBody CreateRecordRequest request)
    {
        try {
            EvaluationRecord record = evaluationRecordService.createEvaluationRecord(
                request.getStudentId(), 
                request.getBankId(), 
                request.getQuestionIds()
            );
            return success(record);
        } catch (Exception e) {
            return error("创建评价记录失败：" + e.getMessage());
        }
    }

    /**
     * 提交答题记录
     */
    @Log(title = "提交答题", businessType = BusinessType.UPDATE)
    @PostMapping("/submit/{recordId}")
    public AjaxResult submitAnswers(@PathVariable("recordId") Long recordId, @RequestBody List<EvaluationRecordAnswer> answerList)
    {
        try {
            int result = evaluationRecordService.submitAnswers(recordId, answerList);
            if (result > 0) {
                // 完成评价记录
                evaluationRecordService.completeEvaluationRecord(recordId);
                return success("答题提交成功");
            } else {
                return error("答题提交失败");
            }
        } catch (Exception e) {
            return error("答题提交失败：" + e.getMessage());
        }
    }

    /**
     * 修改评价记录
     */
    @PreAuthorize("@ss.hasPermi('training:record:list')")
    @Log(title = "评价记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EvaluationRecord evaluationRecord)
    {
        return toAjax(evaluationRecordService.updateEvaluationRecord(evaluationRecord));
    }

    /**
     * 删除评价记录
     */

    @Log(title = "评价记录", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public AjaxResult remove(@RequestBody Long[] recordIds)
    {
        return toAjax(evaluationRecordService.deleteEvaluationRecordByRecordIds(recordIds));
    }

    /**
     * 创建评价记录请求对象
     */
    public static class CreateRecordRequest {
        private Long studentId;
        private Long bankId;
        private Long[] questionIds;

        public Long getStudentId() {
            return studentId;
        }

        public void setStudentId(Long studentId) {
            this.studentId = studentId;
        }

        public Long getBankId() {
            return bankId;
        }

        public void setBankId(Long bankId) {
            this.bankId = bankId;
        }

        public Long[] getQuestionIds() {
            return questionIds;
        }

        public void setQuestionIds(Long[] questionIds) {
            this.questionIds = questionIds;
        }
    }
}
