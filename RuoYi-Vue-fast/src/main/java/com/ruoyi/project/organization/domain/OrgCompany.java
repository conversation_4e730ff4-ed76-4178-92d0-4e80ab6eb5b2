package com.ruoyi.project.organization.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 公司对象 org_company
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public class OrgCompany extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 企业id */
    private Long companyId;

    /** 企业名称 */
    @Excel(name = "企业名称")
    private String companyName;

    /** 统一社会信用代码 */
    @Excel(name = "统一社会信用代码")
    private String socialCode;

    /** 法人 */
    @Excel(name = "法人")
    private String legalPerson;

    /** 所在地址 */
    @Excel(name = "所在地址")
    private String address;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contactInfo;

    /** 提供岗位数 */
    @Excel(name = "提供岗位数")
    private Long jobCount;

    /** 签约学生数 */
    @Excel(name = "签约学生数")
    private Long signedCount;

    public void setCompanyId(Long companyId) 
    {
        this.companyId = companyId;
    }

    public Long getCompanyId() 
    {
        return companyId;
    }

    public void setCompanyName(String companyName) 
    {
        this.companyName = companyName;
    }

    public String getCompanyName() 
    {
        return companyName;
    }

    public void setSocialCode(String socialCode) 
    {
        this.socialCode = socialCode;
    }

    public String getSocialCode() 
    {
        return socialCode;
    }

    public void setLegalPerson(String legalPerson) 
    {
        this.legalPerson = legalPerson;
    }

    public String getLegalPerson() 
    {
        return legalPerson;
    }

    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }

    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }

    public void setContactInfo(String contactInfo) 
    {
        this.contactInfo = contactInfo;
    }

    public String getContactInfo() 
    {
        return contactInfo;
    }

    public void setJobCount(Long jobCount) 
    {
        this.jobCount = jobCount;
    }

    public Long getJobCount() 
    {
        return jobCount;
    }

    public void setSignedCount(Long signedCount) 
    {
        this.signedCount = signedCount;
    }

    public Long getSignedCount() 
    {
        return signedCount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("companyId", getCompanyId())
            .append("companyName", getCompanyName())
            .append("socialCode", getSocialCode())
            .append("legalPerson", getLegalPerson())
            .append("address", getAddress())
            .append("contactPerson", getContactPerson())
            .append("contactInfo", getContactInfo())
            .append("jobCount", getJobCount())
            .append("signedCount", getSignedCount())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
