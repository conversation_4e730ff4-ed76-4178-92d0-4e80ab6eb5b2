package com.ruoyi.project.organization.mapper;

import java.util.List;
import com.ruoyi.project.organization.domain.OrgTeacher;

/**
 * 教师信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface OrgTeacherMapper 
{
    /**
     * 查询教师信息
     * 
     * @param teacherId 教师信息主键
     * @return 教师信息
     */
    public OrgTeacher selectOrgTeacherByTeacherId(Long teacherId);

    /**
     * 查询教师信息列表
     * 
     * @param orgTeacher 教师信息
     * @return 教师信息集合
     */
    public List<OrgTeacher> selectOrgTeacherList(OrgTeacher orgTeacher);

    /**
     * 新增教师信息
     * 
     * @param orgTeacher 教师信息
     * @return 结果
     */
    public int insertOrgTeacher(OrgTeacher orgTeacher);

    /**
     * 修改教师信息
     * 
     * @param orgTeacher 教师信息
     * @return 结果
     */
    public int updateOrgTeacher(OrgTeacher orgTeacher);

    /**
     * 删除教师信息
     * 
     * @param teacherId 教师信息主键
     * @return 结果
     */
    public int deleteOrgTeacherByTeacherId(Long teacherId);

    /**
     * 批量删除教师信息
     * 
     * @param teacherIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrgTeacherByTeacherIds(Long[] teacherIds);
    /**
     * 清空所有教师数据
     *
     * @return 结果
     */
    public int deleteAllOrgTeacher();
}
