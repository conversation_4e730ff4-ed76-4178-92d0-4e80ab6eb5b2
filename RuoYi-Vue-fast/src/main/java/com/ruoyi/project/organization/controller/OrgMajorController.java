package com.ruoyi.project.organization.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.training.domain.TrainingProgramImport;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.organization.domain.OrgMajor;
import com.ruoyi.project.organization.service.IOrgMajorService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 专业Controller
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@RestController
@RequestMapping("/organization/major")
public class OrgMajorController extends BaseController
{
    @Autowired
    private IOrgMajorService orgMajorService;

    /**
     * 查询专业列表
     */
    @PreAuthorize("@ss.hasPermi('organization:major:list')")
    @GetMapping("/list")
    public TableDataInfo list(OrgMajor orgMajor)
    {
        startPage();
        List<OrgMajor> list = orgMajorService.selectOrgMajorList(orgMajor);
        return getDataTable(list);
    }

    /**
     * 导出专业列表
     */
    @PreAuthorize("@ss.hasPermi('organization:major:export')")
    @Log(title = "专业", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OrgMajor orgMajor)
    {
        List<OrgMajor> list = orgMajorService.selectOrgMajorList(orgMajor);
        ExcelUtil<OrgMajor> util = new ExcelUtil<OrgMajor>(OrgMajor.class);
        util.exportExcel(response, list, "专业数据");
    }

    /**
     * 获取专业详细信息
     */
    @PreAuthorize("@ss.hasPermi('organization:major:query')")
    @GetMapping(value = "/{majorId}")
    public AjaxResult getInfo(@PathVariable("majorId") Long majorId)
    {
        return success(orgMajorService.selectOrgMajorByMajorId(majorId));
    }

    /**
     * 新增专业
     */
    @PreAuthorize("@ss.hasPermi('organization:major:add')")
    @Log(title = "专业", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OrgMajor orgMajor)
    {
        orgMajor.setCreateBy(getUsername());
        return toAjax(orgMajorService.insertOrgMajor(orgMajor));
    }

    /**
     * 修改专业
     */
    @PreAuthorize("@ss.hasPermi('organization:major:edit')")
    @Log(title = "专业", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OrgMajor orgMajor)
    {
        orgMajor.setUpdateBy(getUsername());
        return toAjax(orgMajorService.updateOrgMajor(orgMajor));
    }

    /**
     * 删除专业
     */
    @PreAuthorize("@ss.hasPermi('organization:major:remove')")
    @Log(title = "专业", businessType = BusinessType.DELETE)
	@DeleteMapping("/{majorIds}")
    public AjaxResult remove(@PathVariable Long[] majorIds)
    {
        return toAjax(orgMajorService.deleteOrgMajorByMajorIds(majorIds));
    }
    /**
     * 导入专业数据
     */
    @Log(title = "专业", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('organization:major:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file,
                                 @RequestParam(value = "updateSupport", defaultValue = "false") boolean updateSupport,
                                 @RequestParam(value = "overwrite", defaultValue = "false") boolean overwrite) throws Exception
    {
        ExcelUtil<OrgMajor> util = new ExcelUtil<OrgMajor>(OrgMajor.class);
        List<OrgMajor> majorList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = orgMajorService.importProgram(majorList, updateSupport, overwrite, operName);
        return success(message);
    }
    /**
     * 下载专业导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<OrgMajor> util = new ExcelUtil<OrgMajor>(OrgMajor.class);
        util.importTemplateExcel(response, "专业数据");
    }
}
