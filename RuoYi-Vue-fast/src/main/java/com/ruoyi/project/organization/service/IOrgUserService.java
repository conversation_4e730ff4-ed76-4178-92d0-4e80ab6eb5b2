package com.ruoyi.project.organization.service;

import java.util.List;

import com.ruoyi.project.organization.domain.OrgMajor;
import com.ruoyi.project.organization.domain.OrgUser;

/**
 * 机构人员Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface IOrgUserService 
{
    /**
     * 查询机构人员
     * 
     * @param userId 机构人员主键
     * @return 机构人员
     */
    public OrgUser selectOrgUserByUserId(Long userId);

    /**
     * 查询机构人员列表
     * 
     * @param orgUser 机构人员
     * @return 机构人员集合
     */
    public List<OrgUser> selectOrgUserList(OrgUser orgUser);

    /**
     * 新增机构人员
     * 
     * @param orgUser 机构人员
     * @return 结果
     */
    public int insertOrgUser(OrgUser orgUser);

    /**
     * 修改机构人员
     * 
     * @param orgUser 机构人员
     * @return 结果
     */
    public int updateOrgUser(OrgUser orgUser);

    /**
     * 批量删除机构人员
     * 
     * @param userIds 需要删除的机构人员主键集合
     * @return 结果
     */
    public int deleteOrgUserByUserIds(Long[] userIds);

    /**
     * 删除机构人员信息
     * 
     * @param userId 机构人员主键
     * @return 结果
     */
    public int deleteOrgUserByUserId(Long userId);
    /**
     * 导入人员数据
     *
     * @param programList 专业数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param isOverwrite 是否覆盖数据，如果为true则先清空所有数据再导入
     * @param operName 操作用户
     * @return 结果
     */
    public String importProgram(List<OrgUser> programList, Boolean isUpdateSupport, Boolean isOverwrite, String operName);

}
