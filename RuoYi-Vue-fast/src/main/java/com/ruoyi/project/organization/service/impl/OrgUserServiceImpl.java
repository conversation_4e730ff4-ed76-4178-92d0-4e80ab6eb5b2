package com.ruoyi.project.organization.service.impl;

import java.util.List;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.organization.mapper.OrgUserMapper;
import com.ruoyi.project.organization.domain.OrgUser;
import com.ruoyi.project.organization.service.IOrgUserService;

/**
 * 机构人员Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
public class OrgUserServiceImpl implements IOrgUserService 
{
    private static final Logger log = LoggerFactory.getLogger(OrgUserServiceImpl.class);
    @Autowired
    private OrgUserMapper orgUserMapper;

    /**
     * 查询机构人员
     * 
     * @param userId 机构人员主键
     * @return 机构人员
     */
    @Override
    public OrgUser selectOrgUserByUserId(Long userId)
    {
        return orgUserMapper.selectOrgUserByUserId(userId);
    }

    /**
     * 查询机构人员列表
     * 
     * @param orgUser 机构人员
     * @return 机构人员
     */
    @Override
    public List<OrgUser> selectOrgUserList(OrgUser orgUser)
    {
        return orgUserMapper.selectOrgUserList(orgUser);
    }

    /**
     * 新增机构人员
     * 
     * @param orgUser 机构人员
     * @return 结果
     */
    @Override
    public int insertOrgUser(OrgUser orgUser)
    {
        orgUser.setCreateTime(DateUtils.getNowDate());
        return orgUserMapper.insertOrgUser(orgUser);
    }

    /**
     * 修改机构人员
     * 
     * @param orgUser 机构人员
     * @return 结果
     */
    @Override
    public int updateOrgUser(OrgUser orgUser)
    {
        orgUser.setUpdateTime(DateUtils.getNowDate());
        return orgUserMapper.updateOrgUser(orgUser);
    }

    /**
     * 批量删除机构人员
     * 
     * @param userIds 需要删除的机构人员主键
     * @return 结果
     */
    @Override
    public int deleteOrgUserByUserIds(Long[] userIds)
    {
        return orgUserMapper.deleteOrgUserByUserIds(userIds);
    }

    /**
     * 删除机构人员信息
     * 
     * @param userId 机构人员主键
     * @return 结果
     */
    @Override
    public int deleteOrgUserByUserId(Long userId)
    {
        return orgUserMapper.deleteOrgUserByUserId(userId);
    }
    /**
     * 导入用户数据
     *
     * @param userList 用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param isOverwrite 是否覆盖数据，如果为true则先清空所有数据再导入
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importProgram(List<OrgUser> userList, Boolean isUpdateSupport, Boolean isOverwrite, String operName)
    {
        if (StringUtils.isNull(userList) || userList.size() == 0)
        {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        // 如果需要覆盖数据，先清空所有数据
        if (isOverwrite)
        {
            orgUserMapper.deleteAllOrgUser();
        }

        for (OrgUser user : userList)
        {
            try
            {
                // 转换为OrgUser对象
                OrgUser u = new OrgUser();
                u.setUserName(user.getUserName());
                u.setOrgName(user.getOrgName());
                u.setPosition(user.getPosition());
                u.setContactInfo(user.getContactInfo());
                u.setGender(user.getGender());
                u.setWorkStatus(user.getWorkStatus());

                u.setCreateBy(operName);
                u.setCreateTime(DateUtils.getNowDate());
                orgUserMapper.insertOrgUser(u);
                successNum++;
                successMsg.append("<br/>" + successNum + "、用户名称 " + u.getUserName() + " 导入成功");
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、用户名称 " + user.getUserName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
