package com.ruoyi.project.organization.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 机构人员对象 org_user
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class OrgUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 人员id */
    private Long userId;

    /** 人员姓名 */
    @Excel(name = "人员姓名")
    private String userName;

    /** 所属机构 */
    @Excel(name = "所属机构")
    private String orgName;

    /** 职务 */
    @Excel(name = "职务")
    private String position;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contactInfo;

    /** 性别 */
    @Excel(name = "性别")
    private String gender;

    /** 在职状态 */
    @Excel(name = "在职状态")
    private String workStatus;

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }

    public void setOrgName(String orgName)
    {
        this.orgName = orgName;
    }

    public String getOrgName()
    {
        return orgName;
    }

    public void setPosition(String position) 
    {
        this.position = position;
    }

    public String getPosition() 
    {
        return position;
    }

    public void setContactInfo(String contactInfo) 
    {
        this.contactInfo = contactInfo;
    }

    public String getContactInfo() 
    {
        return contactInfo;
    }

    public void setGender(String gender) 
    {
        this.gender = gender;
    }

    public String getGender() 
    {
        return gender;
    }

    public void setWorkStatus(String workStatus) 
    {
        this.workStatus = workStatus;
    }

    public String getWorkStatus() 
    {
        return workStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("orgName", getOrgName())
            .append("position", getPosition())
            .append("contactInfo", getContactInfo())
            .append("gender", getGender())
            .append("workStatus", getWorkStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
