package com.ruoyi.project.organization.service;

import java.util.List;
import com.ruoyi.project.organization.domain.OrgStructure;

/**
 * 机构信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface IOrgStructureService 
{
    /**
     * 查询机构信息
     * 
     * @param structureId 机构信息主键
     * @return 机构信息
     */
    public OrgStructure selectOrgStructureByStructureId(Long structureId);

    /**
     * 查询机构信息列表
     * 
     * @param orgStructure 机构信息
     * @return 机构信息集合
     */
    public List<OrgStructure> selectOrgStructureList(OrgStructure orgStructure);

    /**
     * 新增机构信息
     * 
     * @param orgStructure 机构信息
     * @return 结果
     */
    public int insertOrgStructure(OrgStructure orgStructure);

    /**
     * 修改机构信息
     * 
     * @param orgStructure 机构信息
     * @return 结果
     */
    public int updateOrgStructure(OrgStructure orgStructure);

    /**
     * 批量删除机构信息
     * 
     * @param structureIds 需要删除的机构信息主键集合
     * @return 结果
     */
    public int deleteOrgStructureByStructureIds(Long[] structureIds);

    /**
     * 删除机构信息信息
     * 
     * @param structureId 机构信息主键
     * @return 结果
     */
    public int deleteOrgStructureByStructureId(Long structureId);
}
