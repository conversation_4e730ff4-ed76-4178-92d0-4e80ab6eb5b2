package com.ruoyi.project.organization.service;

import java.util.List;

import com.ruoyi.project.organization.domain.OrgCompany;
import com.ruoyi.project.organization.domain.OrgTeacher;

/**
 * 教师信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface IOrgTeacherService 
{
    /**
     * 查询教师信息
     * 
     * @param teacherId 教师信息主键
     * @return 教师信息
     */
    public OrgTeacher selectOrgTeacherByTeacherId(Long teacherId);

    /**
     * 查询教师信息列表
     * 
     * @param orgTeacher 教师信息
     * @return 教师信息集合
     */
    public List<OrgTeacher> selectOrgTeacherList(OrgTeacher orgTeacher);

    /**
     * 新增教师信息
     * 
     * @param orgTeacher 教师信息
     * @return 结果
     */
    public int insertOrgTeacher(OrgTeacher orgTeacher);

    /**
     * 修改教师信息
     * 
     * @param orgTeacher 教师信息
     * @return 结果
     */
    public int updateOrgTeacher(OrgTeacher orgTeacher);

    /**
     * 批量删除教师信息
     * 
     * @param teacherIds 需要删除的教师信息主键集合
     * @return 结果
     */
    public int deleteOrgTeacherByTeacherIds(Long[] teacherIds);

    /**
     * 删除教师信息信息
     * 
     * @param teacherId 教师信息主键
     * @return 结果
     */
    public int deleteOrgTeacherByTeacherId(Long teacherId);
    /**
     * 导入企业数据
     *
     * @param teacherList 企业数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param isOverwrite 是否覆盖数据，如果为true则先清空所有数据再导入
     * @param operName 操作用户
     * @return 结果
     */
    public String importProgram(List<OrgTeacher> teacherList, Boolean isUpdateSupport, Boolean isOverwrite, String operName);
}
