package com.ruoyi.project.organization.mapper;

import java.util.List;
import com.ruoyi.project.organization.domain.OrgMajor;

/**
 * 专业Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface OrgMajorMapper 
{
    /**
     * 查询专业
     * 
     * @param majorId 专业主键
     * @return 专业
     */
    public OrgMajor selectOrgMajorByMajorId(Long majorId);

    /**
     * 查询专业列表
     * 
     * @param orgMajor 专业
     * @return 专业集合
     */
    public List<OrgMajor> selectOrgMajorList(OrgMajor orgMajor);

    /**
     * 新增专业
     * 
     * @param orgMajor 专业
     * @return 结果
     */
    public int insertOrgMajor(OrgMajor orgMajor);

    /**
     * 修改专业
     * 
     * @param orgMajor 专业
     * @return 结果
     */
    public int updateOrgMajor(OrgMajor orgMajor);

    /**
     * 删除专业
     * 
     * @param majorId 专业主键
     * @return 结果
     */
    public int deleteOrgMajorByMajorId(Long majorId);

    /**
     * 批量删除专业
     * 
     * @param majorIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrgMajorByMajorIds(Long[] majorIds);

    /**
     * 清空所有专业数据
     *
     * @return 结果
     */
    public int deleteAllOrgMajor();
}
