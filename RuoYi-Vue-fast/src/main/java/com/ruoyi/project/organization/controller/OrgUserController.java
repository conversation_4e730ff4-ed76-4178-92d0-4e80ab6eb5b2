package com.ruoyi.project.organization.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.organization.domain.OrgUser;
import com.ruoyi.project.organization.service.IOrgUserService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 机构人员Controller
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@RestController
@RequestMapping("/organization/user")
public class OrgUserController extends BaseController
{
    @Autowired
    private IOrgUserService orgUserService;

    /**
     * 查询机构人员列表
     */
    @PreAuthorize("@ss.hasPermi('user:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(OrgUser orgUser)
    {
        startPage();
        List<OrgUser> list = orgUserService.selectOrgUserList(orgUser);
        return getDataTable(list);
    }

    /**
     * 导出机构人员列表
     */
    @PreAuthorize("@ss.hasPermi('user:user:export')")
    @Log(title = "机构人员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OrgUser orgUser)
    {
        List<OrgUser> list = orgUserService.selectOrgUserList(orgUser);
        ExcelUtil<OrgUser> util = new ExcelUtil<OrgUser>(OrgUser.class);
        util.exportExcel(response, list, "机构人员数据");
    }

    /**
     * 获取机构人员详细信息
     */
    @PreAuthorize("@ss.hasPermi('user:user:query')")
    @GetMapping(value = "/{userId}")
    public AjaxResult getInfo(@PathVariable("userId") Long userId)
    {
        return success(orgUserService.selectOrgUserByUserId(userId));
    }

    /**
     * 新增机构人员
     */
    @PreAuthorize("@ss.hasPermi('user:user:add')")
    @Log(title = "机构人员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OrgUser orgUser)
    {
        orgUser.setCreateBy(getUsername());
        return toAjax(orgUserService.insertOrgUser(orgUser));
    }

    /**
     * 修改机构人员
     */
    @PreAuthorize("@ss.hasPermi('user:user:edit')")
    @Log(title = "机构人员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OrgUser orgUser)
    {
        orgUser.setUpdateBy(getUsername());
        return toAjax(orgUserService.updateOrgUser(orgUser));
    }

    /**
     * 删除机构人员
     */
    @PreAuthorize("@ss.hasPermi('user:user:remove')")
    @Log(title = "机构人员", businessType = BusinessType.DELETE)
	@DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds)
    {
        return toAjax(orgUserService.deleteOrgUserByUserIds(userIds));
    }
    /**
     * 导入用户数据
     */
    @Log(title = "用户", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('organization:user:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file,
                                 @RequestParam(value = "updateSupport", defaultValue = "false") boolean updateSupport,
                                 @RequestParam(value = "overwrite", defaultValue = "false") boolean overwrite) throws Exception
    {
        ExcelUtil<OrgUser> util = new ExcelUtil<OrgUser>(OrgUser.class);
        List<OrgUser> userList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = orgUserService.importProgram(userList, updateSupport, overwrite, operName);
        return success(message);
    }

    /**
     * 下载用户导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<OrgUser> util = new ExcelUtil<OrgUser>(OrgUser.class);
        util.importTemplateExcel(response, "用户数据");
    }
}
