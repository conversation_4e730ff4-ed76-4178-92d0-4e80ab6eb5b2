package com.ruoyi.project.organization.mapper;

import java.util.List;
import com.ruoyi.project.organization.domain.OrgCompany;

/**
 * 公司Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface OrgCompanyMapper 
{
    /**
     * 查询公司
     * 
     * @param companyId 公司主键
     * @return 公司
     */
    public OrgCompany selectOrgCompanyByCompanyId(Long companyId);

    /**
     * 查询公司列表
     * 
     * @param orgCompany 公司
     * @return 公司集合
     */
    public List<OrgCompany> selectOrgCompanyList(OrgCompany orgCompany);

    /**
     * 新增公司
     * 
     * @param orgCompany 公司
     * @return 结果
     */
    public int insertOrgCompany(OrgCompany orgCompany);

    /**
     * 修改公司
     * 
     * @param orgCompany 公司
     * @return 结果
     */
    public int updateOrgCompany(OrgCompany orgCompany);

    /**
     * 删除公司
     * 
     * @param companyId 公司主键
     * @return 结果
     */
    public int deleteOrgCompanyByCompanyId(Long companyId);

    /**
     * 批量删除公司
     * 
     * @param companyIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrgCompanyByCompanyIds(Long[] companyIds);
    /**
     * 清空所有企业数据
     *
     * @return 结果
     */
    public int deleteAllOrgCompany();
}
