package com.ruoyi.project.organization.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.organization.domain.OrgStructure;
import com.ruoyi.project.organization.service.IOrgStructureService;
import com.ruoyi.common.utils.poi.ExcelUtil;

/**
 * 机构信息Controller
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@RestController
@RequestMapping("/organization/structure")
public class OrgStructureController extends BaseController
{
    @Autowired
    private IOrgStructureService orgStructureService;

    /**
     * 查询机构信息列表
     */
    @PreAuthorize("@ss.hasPermi('structure:structure:list')")
    @GetMapping("/list")
    public AjaxResult list(OrgStructure orgStructure)
    {
        List<OrgStructure> list = orgStructureService.selectOrgStructureList(orgStructure);
        return success(list);
    }

    /**
     * 导出机构信息列表
     */
    @PreAuthorize("@ss.hasPermi('structure:structure:export')")
    @Log(title = "机构信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OrgStructure orgStructure)
    {
        List<OrgStructure> list = orgStructureService.selectOrgStructureList(orgStructure);
        ExcelUtil<OrgStructure> util = new ExcelUtil<OrgStructure>(OrgStructure.class);
        util.exportExcel(response, list, "机构信息数据");
    }

    /**
     * 获取机构信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('structure:structure:query')")
    @GetMapping(value = "/{structureId}")
    public AjaxResult getInfo(@PathVariable("structureId") Long structureId)
    {
        return success(orgStructureService.selectOrgStructureByStructureId(structureId));
    }

    /**
     * 新增机构信息
     */
    @PreAuthorize("@ss.hasPermi('structure:structure:add')")
    @Log(title = "机构信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OrgStructure orgStructure)
    {
        orgStructure.setCreateBy(getUsername());
        return toAjax(orgStructureService.insertOrgStructure(orgStructure));
    }

    /**
     * 修改机构信息
     */
    @PreAuthorize("@ss.hasPermi('structure:structure:edit')")
    @Log(title = "机构信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OrgStructure orgStructure)
    {
        orgStructure.setUpdateBy(getUsername());
        return toAjax(orgStructureService.updateOrgStructure(orgStructure));
    }

    /**
     * 删除机构信息
     */
    @PreAuthorize("@ss.hasPermi('structure:structure:remove')")
    @Log(title = "机构信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{structureIds}")
    public AjaxResult remove(@PathVariable Long[] structureIds)
    {
        return toAjax(orgStructureService.deleteOrgStructureByStructureIds(structureIds));
    }
}
