package com.ruoyi.project.organization.mapper;

import java.util.List;
import com.ruoyi.project.organization.domain.OrgUser;

/**
 * 机构人员Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface OrgUserMapper 
{
    /**
     * 查询机构人员
     * 
     * @param userId 机构人员主键
     * @return 机构人员
     */
    public OrgUser selectOrgUserByUserId(Long userId);

    /**
     * 查询机构人员列表
     * 
     * @param orgUser 机构人员
     * @return 机构人员集合
     */
    public List<OrgUser> selectOrgUserList(OrgUser orgUser);

    /**
     * 新增机构人员
     * 
     * @param orgUser 机构人员
     * @return 结果
     */
    public int insertOrgUser(OrgUser orgUser);

    /**
     * 修改机构人员
     * 
     * @param orgUser 机构人员
     * @return 结果
     */
    public int updateOrgUser(OrgUser orgUser);

    /**
     * 删除机构人员
     * 
     * @param userId 机构人员主键
     * @return 结果
     */
    public int deleteOrgUserByUserId(Long userId);

    /**
     * 批量删除机构人员
     * 
     * @param userIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrgUserByUserIds(Long[] userIds);
    /**
     * 清空所有人员数据
     *
     * @return 结果
     */
    public int deleteAllOrgUser();
}
