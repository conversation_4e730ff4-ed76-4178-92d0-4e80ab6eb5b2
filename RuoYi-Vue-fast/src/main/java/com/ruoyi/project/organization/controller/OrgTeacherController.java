package com.ruoyi.project.organization.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.organization.domain.OrgTeacher;
import com.ruoyi.project.organization.service.IOrgTeacherService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 教师信息Controller
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@RestController
@RequestMapping("/organization/teacher")
public class OrgTeacherController extends BaseController
{
    @Autowired
    private IOrgTeacherService orgTeacherService;

    /**
     * 查询教师信息列表
     */
    @PreAuthorize("@ss.hasPermi('organization:teacher:list')")
    @GetMapping("/list")
    public TableDataInfo list(OrgTeacher orgTeacher)
    {
        startPage();
        List<OrgTeacher> list = orgTeacherService.selectOrgTeacherList(orgTeacher);
        return getDataTable(list);
    }

    /**
     * 导出教师信息列表
     */
    @PreAuthorize("@ss.hasPermi('organization:teacher:export')")
    @Log(title = "教师信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OrgTeacher orgTeacher)
    {
        List<OrgTeacher> list = orgTeacherService.selectOrgTeacherList(orgTeacher);
        ExcelUtil<OrgTeacher> util = new ExcelUtil<OrgTeacher>(OrgTeacher.class);
        util.exportExcel(response, list, "教师信息数据");
    }

    /**
     * 获取教师信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('organization:teacher:query')")
    @GetMapping(value = "/{teacherId}")
    public AjaxResult getInfo(@PathVariable("teacherId") Long teacherId)
    {
        return success(orgTeacherService.selectOrgTeacherByTeacherId(teacherId));
    }

    /**
     * 新增教师信息
     */
    @PreAuthorize("@ss.hasPermi('organization:teacher:add')")
    @Log(title = "教师信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OrgTeacher orgTeacher)
    {
        return toAjax(orgTeacherService.insertOrgTeacher(orgTeacher));
    }

    /**
     * 修改教师信息
     */
    @PreAuthorize("@ss.hasPermi('organization:teacher:edit')")
    @Log(title = "教师信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OrgTeacher orgTeacher)
    {
        return toAjax(orgTeacherService.updateOrgTeacher(orgTeacher));
    }

    /**
     * 删除教师信息
     */
    @PreAuthorize("@ss.hasPermi('organization:teacher:remove')")
    @Log(title = "教师信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{teacherIds}")
    public AjaxResult remove(@PathVariable Long[] teacherIds)
    {
        return toAjax(orgTeacherService.deleteOrgTeacherByTeacherIds(teacherIds));
    }
    /**
     * 导入教师数据
     */
    @Log(title = "教师", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('organization:teacher:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file,
                                 @RequestParam(value = "updateSupport", defaultValue = "false") boolean updateSupport,
                                 @RequestParam(value = "overwrite", defaultValue = "false") boolean overwrite) throws Exception
    {
        ExcelUtil<OrgTeacher> util = new ExcelUtil<OrgTeacher>(OrgTeacher.class);
        List<OrgTeacher> teacherList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = orgTeacherService.importProgram(teacherList, updateSupport, overwrite, operName);
        return success(message);
    }

    /**
     * 下载教师导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<OrgTeacher> util = new ExcelUtil<OrgTeacher>(OrgTeacher.class);
        util.importTemplateExcel(response, "教师数据");
    }
}
