package com.ruoyi.project.organization.service;

import java.util.List;
import com.ruoyi.project.organization.domain.OrgCompany;

/**
 * 公司Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface IOrgCompanyService 
{
    /**
     * 查询公司
     * 
     * @param companyId 公司主键
     * @return 公司
     */
    public OrgCompany selectOrgCompanyByCompanyId(Long companyId);

    /**
     * 查询公司列表
     * 
     * @param orgCompany 公司
     * @return 公司集合
     */
    public List<OrgCompany> selectOrgCompanyList(OrgCompany orgCompany);

    /**
     * 新增公司
     * 
     * @param orgCompany 公司
     * @return 结果
     */
    public int insertOrgCompany(OrgCompany orgCompany);

    /**
     * 修改公司
     * 
     * @param orgCompany 公司
     * @return 结果
     */
    public int updateOrgCompany(OrgCompany orgCompany);

    /**
     * 批量删除公司
     * 
     * @param companyIds 需要删除的公司主键集合
     * @return 结果
     */
    public int deleteOrgCompanyByCompanyIds(Long[] companyIds);

    /**
     * 删除公司信息
     * 
     * @param companyId 公司主键
     * @return 结果
     */
    public int deleteOrgCompanyByCompanyId(Long companyId);
    /**
     * 导入企业数据
     *
     * @param companyList 企业数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param isOverwrite 是否覆盖数据，如果为true则先清空所有数据再导入
     * @param operName 操作用户
     * @return 结果
     */
    public String importProgram(List<OrgCompany> companyList, Boolean isUpdateSupport, Boolean isOverwrite, String operName);
}
