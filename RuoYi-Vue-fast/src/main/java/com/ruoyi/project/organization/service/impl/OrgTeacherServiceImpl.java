package com.ruoyi.project.organization.service.impl;

import java.util.List;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.organization.mapper.OrgTeacherMapper;
import com.ruoyi.project.organization.domain.OrgTeacher;
import com.ruoyi.project.organization.service.IOrgTeacherService;

/**
 * 教师信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
public class OrgTeacherServiceImpl implements IOrgTeacherService 
{
    private static final Logger log = LoggerFactory.getLogger(OrgTeacherServiceImpl.class);
    @Autowired
    private OrgTeacherMapper orgTeacherMapper;

    /**
     * 查询教师信息
     * 
     * @param teacherId 教师信息主键
     * @return 教师信息
     */
    @Override
    public OrgTeacher selectOrgTeacherByTeacherId(Long teacherId)
    {
        return orgTeacherMapper.selectOrgTeacherByTeacherId(teacherId);
    }

    /**
     * 查询教师信息列表
     * 
     * @param orgTeacher 教师信息
     * @return 教师信息
     */
    @Override
    public List<OrgTeacher> selectOrgTeacherList(OrgTeacher orgTeacher)
    {
        return orgTeacherMapper.selectOrgTeacherList(orgTeacher);
    }

    /**
     * 新增教师信息
     * 
     * @param orgTeacher 教师信息
     * @return 结果
     */
    @Override
    public int insertOrgTeacher(OrgTeacher orgTeacher)
    {
        orgTeacher.setCreateTime(DateUtils.getNowDate());
        return orgTeacherMapper.insertOrgTeacher(orgTeacher);
    }

    /**
     * 修改教师信息
     * 
     * @param orgTeacher 教师信息
     * @return 结果
     */
    @Override
    public int updateOrgTeacher(OrgTeacher orgTeacher)
    {
        orgTeacher.setUpdateTime(DateUtils.getNowDate());
        return orgTeacherMapper.updateOrgTeacher(orgTeacher);
    }

    /**
     * 批量删除教师信息
     * 
     * @param teacherIds 需要删除的教师信息主键
     * @return 结果
     */
    @Override
    public int deleteOrgTeacherByTeacherIds(Long[] teacherIds)
    {
        return orgTeacherMapper.deleteOrgTeacherByTeacherIds(teacherIds);
    }

    /**
     * 删除教师信息信息
     * 
     * @param teacherId 教师信息主键
     * @return 结果
     */
    @Override
    public int deleteOrgTeacherByTeacherId(Long teacherId)
    {
        return orgTeacherMapper.deleteOrgTeacherByTeacherId(teacherId);
    }
    /**
     * 导入教师数据
     *
     * @param teacherList 教师数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param isOverwrite 是否覆盖数据，如果为true则先清空所有数据再导入
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importProgram(List<OrgTeacher> teacherList, Boolean isUpdateSupport, Boolean isOverwrite, String operName)
    {
        if (StringUtils.isNull(teacherList) || teacherList.size() == 0)
        {
            throw new ServiceException("导入教师数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        // 如果需要覆盖数据，先清空所有数据
        if (isOverwrite)
        {
            orgTeacherMapper.deleteAllOrgTeacher();
        }

        for (OrgTeacher teacher : teacherList)
        {
            try
            {
                // 转换为OrgTeacher对象
                OrgTeacher t = new OrgTeacher();
                t.setTeacherName(teacher.getTeacherName());
                t.setTeacherType(teacher.getTeacherType());
                t.setOrgName(teacher.getOrgName());
                t.setPosition(teacher.getPosition());
                t.setGender(teacher.getGender());
                t.setContactInfo(teacher.getContactInfo());

                t.setCreateBy(operName);
                t.setCreateTime(DateUtils.getNowDate());
                orgTeacherMapper.insertOrgTeacher(t);
                successNum++;
                successMsg.append("<br/>" + successNum + "、教师姓名 " + t.getTeacherName() + " 导入成功");
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、教师姓名 " + teacher.getTeacherName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
