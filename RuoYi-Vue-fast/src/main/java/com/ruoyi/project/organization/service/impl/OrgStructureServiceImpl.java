package com.ruoyi.project.organization.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.organization.mapper.OrgStructureMapper;
import com.ruoyi.project.organization.domain.OrgStructure;
import com.ruoyi.project.organization.service.IOrgStructureService;

/**
 * 机构信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Service
public class OrgStructureServiceImpl implements IOrgStructureService 
{
    @Autowired
    private OrgStructureMapper orgStructureMapper;

    /**
     * 查询机构信息
     * 
     * @param structureId 机构信息主键
     * @return 机构信息
     */
    @Override
    public OrgStructure selectOrgStructureByStructureId(Long structureId)
    {
        return orgStructureMapper.selectOrgStructureByStructureId(structureId);
    }

    /**
     * 查询机构信息列表
     * 
     * @param orgStructure 机构信息
     * @return 机构信息
     */
    @Override
    public List<OrgStructure> selectOrgStructureList(OrgStructure orgStructure)
    {
        return orgStructureMapper.selectOrgStructureList(orgStructure);
    }

    /**
     * 新增机构信息
     * 
     * @param orgStructure 机构信息
     * @return 结果
     */
    @Override
    public int insertOrgStructure(OrgStructure orgStructure)
    {
        orgStructure.setCreateTime(DateUtils.getNowDate());
        return orgStructureMapper.insertOrgStructure(orgStructure);
    }

    /**
     * 修改机构信息
     * 
     * @param orgStructure 机构信息
     * @return 结果
     */
    @Override
    public int updateOrgStructure(OrgStructure orgStructure)
    {
        orgStructure.setUpdateTime(DateUtils.getNowDate());
        return orgStructureMapper.updateOrgStructure(orgStructure);
    }

    /**
     * 批量删除机构信息
     * 
     * @param structureIds 需要删除的机构信息主键
     * @return 结果
     */
    @Override
    public int deleteOrgStructureByStructureIds(Long[] structureIds)
    {
        return orgStructureMapper.deleteOrgStructureByStructureIds(structureIds);
    }

    /**
     * 删除机构信息信息
     * 
     * @param structureId 机构信息主键
     * @return 结果
     */
    @Override
    public int deleteOrgStructureByStructureId(Long structureId)
    {
        return orgStructureMapper.deleteOrgStructureByStructureId(structureId);
    }
}
