package com.ruoyi.project.organization.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.TreeEntity;

/**
 * 机构信息对象 org_structure
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class OrgStructure extends TreeEntity
{
    private static final long serialVersionUID = 1L;

    /** 机构ID */
    private Long structureId;

    /** 机构名称 */
    @Excel(name = "机构名称")
    private String structureName;

    /** 机构类型 */
    @Excel(name = "机构类型")
    private String structureType;

    /** 机构简称 */
    @Excel(name = "机构简称")
    private String structureAbbreviation;

    /** 负责人 */
    @Excel(name = "负责人")
    private String director;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contactInfo;

    /** 简介 */
    @Excel(name = "简介")
    private String description;

    public void setStructureId(Long structureId) 
    {
        this.structureId = structureId;
    }

    public Long getStructureId() 
    {
        return structureId;
    }

    public void setStructureName(String structureName) 
    {
        this.structureName = structureName;
    }

    public String getStructureName() 
    {
        return structureName;
    }

    public void setStructureType(String structureType) 
    {
        this.structureType = structureType;
    }

    public String getStructureType() 
    {
        return structureType;
    }

    public void setStructureAbbreviation(String structureAbbreviation) 
    {
        this.structureAbbreviation = structureAbbreviation;
    }

    public String getStructureAbbreviation() 
    {
        return structureAbbreviation;
    }

    public void setDirector(String director) 
    {
        this.director = director;
    }

    public String getDirector() 
    {
        return director;
    }

    public void setContactInfo(String contactInfo) 
    {
        this.contactInfo = contactInfo;
    }

    public String getContactInfo() 
    {
        return contactInfo;
    }

    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("structureId", getStructureId())
            .append("structureName", getStructureName())
            .append("parentId", getParentId())
            .append("structureType", getStructureType())
            .append("structureAbbreviation", getStructureAbbreviation())
            .append("director", getDirector())
            .append("contactInfo", getContactInfo())
            .append("description", getDescription())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
