package com.ruoyi.project.organization.service.impl;

import java.util.List;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.organization.mapper.OrgMajorMapper;
import com.ruoyi.project.organization.domain.OrgMajor;
import com.ruoyi.project.organization.service.IOrgMajorService;

/**
 * 专业Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Service
public class OrgMajorServiceImpl implements IOrgMajorService 
{
    private static final Logger log = LoggerFactory.getLogger(OrgMajorServiceImpl.class);
    @Autowired
    private OrgMajorMapper orgMajorMapper;

    /**
     * 查询专业
     * 
     * @param majorId 专业主键
     * @return 专业
     */
    @Override
    public OrgMajor selectOrgMajorByMajorId(Long majorId)
    {
        return orgMajorMapper.selectOrgMajorByMajorId(majorId);
    }

    /**
     * 查询专业列表
     * 
     * @param orgMajor 专业
     * @return 专业
     */
    @Override
    public List<OrgMajor> selectOrgMajorList(OrgMajor orgMajor)
    {
        return orgMajorMapper.selectOrgMajorList(orgMajor);
    }

    /**
     * 新增专业
     * 
     * @param orgMajor 专业
     * @return 结果
     */
    @Override
    public int insertOrgMajor(OrgMajor orgMajor)
    {
        orgMajor.setCreateTime(DateUtils.getNowDate());
        return orgMajorMapper.insertOrgMajor(orgMajor);
    }

    /**
     * 修改专业
     * 
     * @param orgMajor 专业
     * @return 结果
     */
    @Override
    public int updateOrgMajor(OrgMajor orgMajor)
    {
        orgMajor.setUpdateTime(DateUtils.getNowDate());
        return orgMajorMapper.updateOrgMajor(orgMajor);
    }

    /**
     * 批量删除专业
     * 
     * @param majorIds 需要删除的专业主键
     * @return 结果
     */
    @Override
    public int deleteOrgMajorByMajorIds(Long[] majorIds)
    {
        return orgMajorMapper.deleteOrgMajorByMajorIds(majorIds);
    }

    /**
     * 删除专业信息
     * 
     * @param majorId 专业主键
     * @return 结果
     */
    @Override
    public int deleteOrgMajorByMajorId(Long majorId)
    {
        return orgMajorMapper.deleteOrgMajorByMajorId(majorId);
    }

    /**
     * 导入专业数据
     *
     * @param majorList 专业数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param isOverwrite 是否覆盖数据，如果为true则先清空所有数据再导入
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importProgram(List<OrgMajor> majorList, Boolean isUpdateSupport, Boolean isOverwrite, String operName)
    {
        if (StringUtils.isNull(majorList) || majorList.size() == 0)
        {
            throw new ServiceException("导入专业数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        // 如果需要覆盖数据，先清空所有数据
        if (isOverwrite)
        {
            orgMajorMapper.deleteAllOrgMajor();
        }

        for (OrgMajor major : majorList)
        {
            try
            {
                // 转换为TrainingProgram对象
                OrgMajor m = new OrgMajor();
                m.setMajorName(major.getMajorName());
                m.setMajorDesc(major.getMajorDesc());

                m.setCreateBy(operName);
                m.setCreateTime(DateUtils.getNowDate());
                orgMajorMapper.insertOrgMajor(m);
                successNum++;
                successMsg.append("<br/>" + successNum + "、专业名称 " + m.getMajorName() + " 导入成功");
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、专业名称 " + major.getMajorName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
