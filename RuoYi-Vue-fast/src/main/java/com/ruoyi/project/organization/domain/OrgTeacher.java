package com.ruoyi.project.organization.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 教师信息对象 org_teacher
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
public class OrgTeacher extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 教师id */
    private Long teacherId;

    /** 教师姓名 */
    @Excel(name = "教师姓名")
    private String teacherName;

    /** 教师类型 */
    @Excel(name = "教师类型")
    private String teacherType;

    /** 所属组织名称 */
    @Excel(name = "所属组织名称")
    private String orgName;

    /** 职务 */
    @Excel(name = "职务")
    private String position;

    /** 性别 */
    @Excel(name = "性别")
    private String gender;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contactInfo;

    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }

    public void setTeacherName(String teacherName) 
    {
        this.teacherName = teacherName;
    }

    public String getTeacherName() 
    {
        return teacherName;
    }

    public void setTeacherType(String teacherType) 
    {
        this.teacherType = teacherType;
    }

    public String getTeacherType() 
    {
        return teacherType;
    }

    public void setOrgName(String orgName) 
    {
        this.orgName = orgName;
    }

    public String getOrgName() 
    {
        return orgName;
    }

    public void setPosition(String position) 
    {
        this.position = position;
    }

    public String getPosition() 
    {
        return position;
    }

    public void setGender(String gender) 
    {
        this.gender = gender;
    }

    public String getGender() 
    {
        return gender;
    }

    public void setContactInfo(String contactInfo) 
    {
        this.contactInfo = contactInfo;
    }

    public String getContactInfo() 
    {
        return contactInfo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("teacherId", getTeacherId())
            .append("teacherName", getTeacherName())
            .append("teacherType", getTeacherType())
            .append("orgName", getOrgName())
            .append("position", getPosition())
            .append("gender", getGender())
            .append("contactInfo", getContactInfo())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
