package com.ruoyi.project.organization.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.organization.domain.OrgCompany;
import com.ruoyi.project.organization.service.IOrgCompanyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 公司Controller
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@RestController
@RequestMapping("/organization/company")
public class OrgCompanyController extends BaseController
{
    @Autowired
    private IOrgCompanyService orgCompanyService;

    /**
     * 查询公司列表
     */
    @PreAuthorize("@ss.hasPermi('organization:company:list')")
    @GetMapping("/list")
    public TableDataInfo list(OrgCompany orgCompany)
    {
        startPage();
        List<OrgCompany> list = orgCompanyService.selectOrgCompanyList(orgCompany);
        return getDataTable(list);
    }

    /**
     * 导出公司列表
     */
    @PreAuthorize("@ss.hasPermi('organization:company:export')")
    @Log(title = "公司", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OrgCompany orgCompany)
    {
        List<OrgCompany> list = orgCompanyService.selectOrgCompanyList(orgCompany);
        ExcelUtil<OrgCompany> util = new ExcelUtil<OrgCompany>(OrgCompany.class);
        util.exportExcel(response, list, "公司数据");
    }

    /**
     * 获取公司详细信息
     */
    @PreAuthorize("@ss.hasPermi('organization:company:query')")
    @GetMapping(value = "/{companyId}")
    public AjaxResult getInfo(@PathVariable("companyId") Long companyId)
    {
        return success(orgCompanyService.selectOrgCompanyByCompanyId(companyId));
    }

    /**
     * 新增公司
     */
    @PreAuthorize("@ss.hasPermi('organization:company:add')")
    @Log(title = "公司", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OrgCompany orgCompany)
    {
        orgCompany.setCreateBy(getUsername());
        return toAjax(orgCompanyService.insertOrgCompany(orgCompany));
    }

    /**
     * 修改公司
     */
    @PreAuthorize("@ss.hasPermi('organization:company:edit')")
    @Log(title = "公司", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OrgCompany orgCompany)
    {
        orgCompany.setUpdateBy(getUsername());
        return toAjax(orgCompanyService.updateOrgCompany(orgCompany));
    }

    /**
     * 删除公司
     */
    @PreAuthorize("@ss.hasPermi('organization:company:remove')")
    @Log(title = "公司", businessType = BusinessType.DELETE)
	@DeleteMapping("/{companyIds}")
    public AjaxResult remove(@PathVariable Long[] companyIds)
    {
        return toAjax(orgCompanyService.deleteOrgCompanyByCompanyIds(companyIds));
    }

    /**
     * 导入企业数据
     */
    @Log(title = "企业", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('organization:company:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file,
                                 @RequestParam(value = "updateSupport", defaultValue = "false") boolean updateSupport,
                                 @RequestParam(value = "overwrite", defaultValue = "false") boolean overwrite) throws Exception
    {
        ExcelUtil<OrgCompany> util = new ExcelUtil<OrgCompany>(OrgCompany.class);
        List<OrgCompany> companyList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = orgCompanyService.importProgram(companyList, updateSupport, overwrite, operName);
        return success(message);
    }

    /**
     * 下载企业导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<OrgCompany> util = new ExcelUtil<OrgCompany>(OrgCompany.class);
        util.importTemplateExcel(response, "企业数据");
    }
}
