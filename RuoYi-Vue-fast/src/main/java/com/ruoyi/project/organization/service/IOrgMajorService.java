package com.ruoyi.project.organization.service;

import java.util.List;
import com.ruoyi.project.organization.domain.OrgMajor;
import com.ruoyi.project.training.domain.TrainingProgramImport;

/**
 * 专业Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface IOrgMajorService 
{
    /**
     * 查询专业
     * 
     * @param majorId 专业主键
     * @return 专业
     */
    public OrgMajor selectOrgMajorByMajorId(Long majorId);

    /**
     * 查询专业列表
     * 
     * @param orgMajor 专业
     * @return 专业集合
     */
    public List<OrgMajor> selectOrgMajorList(OrgMajor orgMajor);

    /**
     * 新增专业
     * 
     * @param orgMajor 专业
     * @return 结果
     */
    public int insertOrgMajor(OrgMajor orgMajor);

    /**
     * 修改专业
     * 
     * @param orgMajor 专业
     * @return 结果
     */
    public int updateOrgMajor(OrgMajor orgMajor);

    /**
     * 批量删除专业
     * 
     * @param majorIds 需要删除的专业主键集合
     * @return 结果
     */
    public int deleteOrgMajorByMajorIds(Long[] majorIds);

    /**
     * 删除专业信息
     * 
     * @param majorId 专业主键
     * @return 结果
     */
    public int deleteOrgMajorByMajorId(Long majorId);
    /**
     * 导入专业数据
     *
     * @param programList 专业数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param isOverwrite 是否覆盖数据，如果为true则先清空所有数据再导入
     * @param operName 操作用户
     * @return 结果
     */
    public String importProgram(List<OrgMajor> programList, Boolean isUpdateSupport, Boolean isOverwrite, String operName);
}
