package com.ruoyi.project.organization.service.impl;

import java.util.List;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.organization.mapper.OrgCompanyMapper;
import com.ruoyi.project.organization.domain.OrgCompany;
import com.ruoyi.project.organization.service.IOrgCompanyService;

/**
 * 公司Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
public class OrgCompanyServiceImpl implements IOrgCompanyService 
{
    private static final Logger log = LoggerFactory.getLogger(OrgCompanyServiceImpl.class);
    @Autowired
    private OrgCompanyMapper orgCompanyMapper;

    /**
     * 查询公司
     * 
     * @param companyId 公司主键
     * @return 公司
     */
    @Override
    public OrgCompany selectOrgCompanyByCompanyId(Long companyId)
    {
        return orgCompanyMapper.selectOrgCompanyByCompanyId(companyId);
    }

    /**
     * 查询公司列表
     * 
     * @param orgCompany 公司
     * @return 公司
     */
    @Override
    public List<OrgCompany> selectOrgCompanyList(OrgCompany orgCompany)
    {
        return orgCompanyMapper.selectOrgCompanyList(orgCompany);
    }

    /**
     * 新增公司
     * 
     * @param orgCompany 公司
     * @return 结果
     */
    @Override
    public int insertOrgCompany(OrgCompany orgCompany)
    {
        orgCompany.setCreateTime(DateUtils.getNowDate());
        return orgCompanyMapper.insertOrgCompany(orgCompany);
    }

    /**
     * 修改公司
     * 
     * @param orgCompany 公司
     * @return 结果
     */
    @Override
    public int updateOrgCompany(OrgCompany orgCompany)
    {
        orgCompany.setUpdateTime(DateUtils.getNowDate());
        return orgCompanyMapper.updateOrgCompany(orgCompany);
    }

    /**
     * 批量删除公司
     * 
     * @param companyIds 需要删除的公司主键
     * @return 结果
     */
    @Override
    public int deleteOrgCompanyByCompanyIds(Long[] companyIds)
    {
        return orgCompanyMapper.deleteOrgCompanyByCompanyIds(companyIds);
    }

    /**
     * 删除公司信息
     * 
     * @param companyId 公司主键
     * @return 结果
     */
    @Override
    public int deleteOrgCompanyByCompanyId(Long companyId)
    {
        return orgCompanyMapper.deleteOrgCompanyByCompanyId(companyId);
    }
    /**
     * 导入企业数据
     *
     * @param companyList 企业数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param isOverwrite 是否覆盖数据，如果为true则先清空所有数据再导入
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importProgram(List<OrgCompany> companyList, Boolean isUpdateSupport, Boolean isOverwrite, String operName)
    {
        if (StringUtils.isNull(companyList) || companyList.size() == 0)
        {
            throw new ServiceException("导入企业数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        // 如果需要覆盖数据，先清空所有数据
        if (isOverwrite)
        {
            orgCompanyMapper.deleteAllOrgCompany();
        }

        for (OrgCompany company : companyList)
        {
            try
            {
                // 转换为OrgCompany对象
                OrgCompany c = new OrgCompany();
                c.setCompanyName(company.getCompanyName());
                c.setSocialCode(company.getSocialCode());
                c.setLegalPerson(company.getLegalPerson());
                c.setAddress(company.getAddress());
                c.setContactPerson(company.getContactPerson());
                c.setContactInfo(company.getContactInfo());

                c.setCreateBy(operName);
                c.setCreateTime(DateUtils.getNowDate());
                orgCompanyMapper.insertOrgCompany(c);
                successNum++;
                successMsg.append("<br/>" + successNum + "、企业名称 " + c.getCompanyName() + " 导入成功");
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、企业名称 " + company.getCompanyName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
