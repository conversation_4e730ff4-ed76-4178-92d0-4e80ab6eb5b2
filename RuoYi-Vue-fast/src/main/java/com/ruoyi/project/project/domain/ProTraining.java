package com.ruoyi.project.project.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 培训项目对象 pro_training
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
public class ProTraining extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 项目id */
    private Long projectId;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String projectName;

    /** 培训模式 */
    @Excel(name = "培训模式")
    private String trainingMode;

    /** 培训人日 */
    @Excel(name = "培训人日")
    private Long trainingDays;

    /** 项目状态 */
    @Excel(name = "项目状态")
    private String projectStatus;

    /** 培训开始日期 */
    @Excel(name = "培训开始日期")
    private Date startDate;

    /** 培训截止日期 */
    @Excel(name = "培训截止日期")
    private Date endDate;

    /** 项目描述 */
    @Excel(name = "项目描述")
    private String projectDesc;

    public void setProjectId(Long projectId) 
    {
        this.projectId = projectId;
    }

    public Long getProjectId() 
    {
        return projectId;
    }

    public void setProjectName(String projectName) 
    {
        this.projectName = projectName;
    }

    public String getProjectName() 
    {
        return projectName;
    }

    public void setTrainingMode(String trainingMode) 
    {
        this.trainingMode = trainingMode;
    }

    public String getTrainingMode() 
    {
        return trainingMode;
    }

    public void setTrainingDays(Long trainingDays) 
    {
        this.trainingDays = trainingDays;
    }

    public Long getTrainingDays() 
    {
        return trainingDays;
    }

    public void setProjectStatus(String projectStatus) 
    {
        this.projectStatus = projectStatus;
    }

    public String getProjectStatus() 
    {
        return projectStatus;
    }

    public void setStartDate(Date startDate) 
    {
        this.startDate = startDate;
    }

    public Date getStartDate() 
    {
        return startDate;
    }

    public void setEndDate(Date endDate) 
    {
        this.endDate = endDate;
    }

    public Date getEndDate() 
    {
        return endDate;
    }

    public void setProjectDesc(String projectDesc) 
    {
        this.projectDesc = projectDesc;
    }

    public String getProjectDesc() 
    {
        return projectDesc;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("projectId", getProjectId())
            .append("projectName", getProjectName())
            .append("trainingMode", getTrainingMode())
            .append("trainingDays", getTrainingDays())
            .append("projectStatus", getProjectStatus())
            .append("startDate", getStartDate())
            .append("endDate", getEndDate())
            .append("projectDesc", getProjectDesc())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
