package com.ruoyi.project.project.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 资源对象 pro_resource
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public class ProResource extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 资源id */
    private Long resourceId;

    /** 资源名称 */
    @Excel(name = "资源名称")
    private String resourceName;

    /** 资源类别 */
    @Excel(name = "资源类别")
    private String resourceCategory;

    /** 资源附件路径 */
    @Excel(name = "资源附件路径")
    private String resourceAttachment;

    public void setResourceId(Long resourceId) 
    {
        this.resourceId = resourceId;
    }

    public Long getResourceId() 
    {
        return resourceId;
    }

    public void setResourceName(String resourceName) 
    {
        this.resourceName = resourceName;
    }

    public String getResourceName() 
    {
        return resourceName;
    }

    public void setResourceCategory(String resourceCategory) 
    {
        this.resourceCategory = resourceCategory;
    }

    public String getResourceCategory() 
    {
        return resourceCategory;
    }

    public void setResourceAttachment(String resourceAttachment) 
    {
        this.resourceAttachment = resourceAttachment;
    }

    public String getResourceAttachment() 
    {
        return resourceAttachment;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("resourceId", getResourceId())
            .append("resourceName", getResourceName())
            .append("resourceCategory", getResourceCategory())
            .append("resourceAttachment", getResourceAttachment())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
