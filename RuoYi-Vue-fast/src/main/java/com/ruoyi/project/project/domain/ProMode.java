package com.ruoyi.project.project.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 培训模式对象 pro_mode
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
public class ProMode extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 模式id */
    private Long modeId;

    /** 模式名称 */
    @Excel(name = "模式名称")
    private String modeName;

    public void setModeId(Long modeId) 
    {
        this.modeId = modeId;
    }

    public Long getModeId() 
    {
        return modeId;
    }

    public void setModeName(String modeName) 
    {
        this.modeName = modeName;
    }

    public String getModeName() 
    {
        return modeName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("modeId", getModeId())
            .append("modeName", getModeName())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
