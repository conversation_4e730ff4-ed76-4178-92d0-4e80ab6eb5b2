package com.ruoyi.project.project.mapper;

import java.util.List;
import com.ruoyi.project.project.domain.ProTraining;

/**
 * 培训项目Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
public interface ProTrainingMapper {
    /**
     * 查询培训项目
     *
     * @param projectId 培训项目主键
     * @return 培训项目
     */
    public ProTraining selectProTrainingByProjectId(Long projectId);

    /**
     * 查询培训项目列表
     *
     * @param proTraining 培训项目
     * @return 培训项目集合
     */
    public List<ProTraining> selectProTrainingList(ProTraining proTraining);

    /**
     * 新增培训项目
     *
     * @param proTraining 培训项目
     * @return 结果
     */
    public int insertProTraining(ProTraining proTraining);

    /**
     * 修改培训项目
     *
     * @param proTraining 培训项目
     * @return 结果
     */
    public int updateProTraining(ProTraining proTraining);

    /**
     * 删除培训项目
     *
     * @param projectId 培训项目主键
     * @return 结果
     */
    public int deleteProTrainingByProjectId(Long projectId);

    /**
     * 批量删除培训项目
     *
     * @param projectIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProTrainingByProjectIds(Long[] projectIds);

    /**
     * 清空所有培训项目
     *
     * @return 结果
     */
    public int deleteAllProTraining();
}
