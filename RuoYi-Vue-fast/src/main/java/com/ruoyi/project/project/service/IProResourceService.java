package com.ruoyi.project.project.service;

import java.util.List;
import com.ruoyi.project.project.domain.ProResource;

/**
 * 资源Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public interface IProResourceService 
{
    /**
     * 查询资源
     * 
     * @param resourceId 资源主键
     * @return 资源
     */
    public ProResource selectProResourceByResourceId(Long resourceId);

    /**
     * 查询资源列表
     * 
     * @param proResource 资源
     * @return 资源集合
     */
    public List<ProResource> selectProResourceList(ProResource proResource);

    /**
     * 新增资源
     * 
     * @param proResource 资源
     * @return 结果
     */
    public int insertProResource(ProResource proResource);

    /**
     * 修改资源
     * 
     * @param proResource 资源
     * @return 结果
     */
    public int updateProResource(ProResource proResource);

    /**
     * 批量删除资源
     * 
     * @param resourceIds 需要删除的资源主键集合
     * @return 结果
     */
    public int deleteProResourceByResourceIds(Long[] resourceIds);

    /**
     * 删除资源信息
     * 
     * @param resourceId 资源主键
     * @return 结果
     */
    public int deleteProResourceByResourceId(Long resourceId);
}
