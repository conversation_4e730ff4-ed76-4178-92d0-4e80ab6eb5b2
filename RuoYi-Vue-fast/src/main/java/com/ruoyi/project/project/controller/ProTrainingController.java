package com.ruoyi.project.project.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.project.domain.ProTraining;
import com.ruoyi.project.project.service.IProTrainingService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 培训项目Controller
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
@RestController
@RequestMapping("/project/training")
public class ProTrainingController extends BaseController
{
    @Autowired
    private IProTrainingService proTrainingService;

    /**
     * 查询培训项目列表
     */
    @PreAuthorize("@ss.hasPermi('project:training:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProTraining proTraining)
    {
        startPage();
        List<ProTraining> list = proTrainingService.selectProTrainingList(proTraining);
        return getDataTable(list);
    }

    /**
     * 导出培训项目列表
     */
    @PreAuthorize("@ss.hasPermi('project:training:export')")
    @Log(title = "培训项目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProTraining proTraining)
    {
        List<ProTraining> list = proTrainingService.selectProTrainingList(proTraining);
        ExcelUtil<ProTraining> util = new ExcelUtil<ProTraining>(ProTraining.class);
        util.exportExcel(response, list, "培训项目数据");
    }

    /**
     * 获取培训项目详细信息
     */
    @PreAuthorize("@ss.hasPermi('project:training:query')")
    @GetMapping(value = "/{projectId}")
    public AjaxResult getInfo(@PathVariable("projectId") Long projectId)
    {
        return success(proTrainingService.selectProTrainingByProjectId(projectId));
    }

    /**
     * 新增培训项目
     */
    @PreAuthorize("@ss.hasPermi('project:training:add')")
    @Log(title = "培训项目", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProTraining proTraining)
    {
        return toAjax(proTrainingService.insertProTraining(proTraining));
    }

    /**
     * 修改培训项目
     */
    @PreAuthorize("@ss.hasPermi('project:training:edit')")
    @Log(title = "培训项目", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProTraining proTraining)
    {
        return toAjax(proTrainingService.updateProTraining(proTraining));
    }

    /**
     * 删除培训项目
     */
    @PreAuthorize("@ss.hasPermi('project:training:remove')")
    @Log(title = "培训项目", businessType = BusinessType.DELETE)
	@DeleteMapping("/{projectIds}")
    public AjaxResult remove(@PathVariable Long[] projectIds)
    {
        return toAjax(proTrainingService.deleteProTrainingByProjectIds(projectIds));
    }
    /**
     * 导入培训模式数据
     */
    @Log(title = "培训模式", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('training:mode:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file,
                                 @RequestParam(value = "updateSupport", defaultValue = "false") boolean updateSupport,
                                 @RequestParam(value = "overwrite", defaultValue = "false") boolean overwrite) throws Exception
    {
        ExcelUtil<ProTraining> util = new ExcelUtil<ProTraining>(ProTraining.class);
        List<ProTraining> trainingList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = proTrainingService.importProgram(trainingList, updateSupport, overwrite, operName);
        return success(message);
    }

    /**
     * 下载培训模式导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<ProTraining> util = new ExcelUtil<ProTraining>(ProTraining.class);
        util.importTemplateExcel(response, "培训模式数据");
    }
}
