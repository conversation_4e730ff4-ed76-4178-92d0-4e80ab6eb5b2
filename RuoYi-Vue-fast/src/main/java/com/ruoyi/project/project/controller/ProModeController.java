package com.ruoyi.project.project.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.project.domain.ProMode;
import com.ruoyi.project.project.service.IProModeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 培训模式Controller
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
@RestController
@RequestMapping("/project/mode")
public class ProModeController extends BaseController
{
    @Autowired
    private IProModeService proModeService;

    /**
     * 查询培训模式列表
     */
    @PreAuthorize("@ss.hasPermi('project:mode:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProMode proMode)
    {
        startPage();
        List<ProMode> list = proModeService.selectProModeList(proMode);
        return getDataTable(list);
    }

    /**
     * 导出培训模式列表
     */
    @PreAuthorize("@ss.hasPermi('project:mode:export')")
    @Log(title = "培训模式", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProMode proMode)
    {
        List<ProMode> list = proModeService.selectProModeList(proMode);
        ExcelUtil<ProMode> util = new ExcelUtil<ProMode>(ProMode.class);
        util.exportExcel(response, list, "培训模式数据");
    }

    /**
     * 获取培训模式详细信息
     */
    @PreAuthorize("@ss.hasPermi('project:mode:query')")
    @GetMapping(value = "/{modeId}")
    public AjaxResult getInfo(@PathVariable("modeId") Long modeId)
    {
        return success(proModeService.selectProModeByModeId(modeId));
    }

    /**
     * 新增培训模式
     */
    @PreAuthorize("@ss.hasPermi('project:mode:add')")
    @Log(title = "培训模式", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProMode proMode)
    {
        proMode.setCreateBy(getUsername());
        return toAjax(proModeService.insertProMode(proMode));
    }

    /**
     * 修改培训模式
     */
    @PreAuthorize("@ss.hasPermi('project:mode:edit')")
    @Log(title = "培训模式", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProMode proMode)
    {
        proMode.setUpdateBy(getUsername());
        return toAjax(proModeService.updateProMode(proMode));
    }

    /**
     * 删除培训模式
     */
    @PreAuthorize("@ss.hasPermi('project:mode:remove')")
    @Log(title = "培训模式", businessType = BusinessType.DELETE)
	@DeleteMapping("/{modeIds}")
    public AjaxResult remove(@PathVariable Long[] modeIds)
    {
        return toAjax(proModeService.deleteProModeByModeIds(modeIds));
    }
    /**
     * 导入培训模式数据
     */
    @Log(title = "培训模式", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('training:mode:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file,
                                 @RequestParam(value = "updateSupport", defaultValue = "false") boolean updateSupport,
                                 @RequestParam(value = "overwrite", defaultValue = "false") boolean overwrite) throws Exception
    {
        ExcelUtil<ProMode> util = new ExcelUtil<ProMode>(ProMode.class);
        List<ProMode> modeList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = proModeService.importProgram(modeList, updateSupport, overwrite, operName);
        return success(message);
    }

    /**
     * 下载培训模式导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<ProMode> util = new ExcelUtil<ProMode>(ProMode.class);
        util.importTemplateExcel(response, "培训模式数据");
    }
}
