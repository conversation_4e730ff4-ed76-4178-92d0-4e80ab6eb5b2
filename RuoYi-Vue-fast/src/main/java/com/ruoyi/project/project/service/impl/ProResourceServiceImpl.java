package com.ruoyi.project.project.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.project.mapper.ProResourceMapper;
import com.ruoyi.project.project.domain.ProResource;
import com.ruoyi.project.project.service.IProResourceService;

/**
 * 资源Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Service
public class ProResourceServiceImpl implements IProResourceService 
{
    @Autowired
    private ProResourceMapper proResourceMapper;

    /**
     * 查询资源
     * 
     * @param resourceId 资源主键
     * @return 资源
     */
    @Override
    public ProResource selectProResourceByResourceId(Long resourceId)
    {
        return proResourceMapper.selectProResourceByResourceId(resourceId);
    }

    /**
     * 查询资源列表
     * 
     * @param proResource 资源
     * @return 资源
     */
    @Override
    public List<ProResource> selectProResourceList(ProResource proResource)
    {
        return proResourceMapper.selectProResourceList(proResource);
    }

    /**
     * 新增资源
     * 
     * @param proResource 资源
     * @return 结果
     */
    @Override
    public int insertProResource(ProResource proResource)
    {
        proResource.setCreateTime(DateUtils.getNowDate());
        return proResourceMapper.insertProResource(proResource);
    }

    /**
     * 修改资源
     * 
     * @param proResource 资源
     * @return 结果
     */
    @Override
    public int updateProResource(ProResource proResource)
    {
        proResource.setUpdateTime(DateUtils.getNowDate());
        return proResourceMapper.updateProResource(proResource);
    }

    /**
     * 批量删除资源
     * 
     * @param resourceIds 需要删除的资源主键
     * @return 结果
     */
    @Override
    public int deleteProResourceByResourceIds(Long[] resourceIds)
    {
        return proResourceMapper.deleteProResourceByResourceIds(resourceIds);
    }

    /**
     * 删除资源信息
     * 
     * @param resourceId 资源主键
     * @return 结果
     */
    @Override
    public int deleteProResourceByResourceId(Long resourceId)
    {
        return proResourceMapper.deleteProResourceByResourceId(resourceId);
    }
}
