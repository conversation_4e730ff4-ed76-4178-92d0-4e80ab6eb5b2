package com.ruoyi.project.project.service;

import java.util.List;

import com.ruoyi.project.project.domain.ProMode;
import com.ruoyi.project.project.domain.ProTraining;

/**
 * 培训项目Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
public interface IProTrainingService 
{
    /**
     * 查询培训项目
     * 
     * @param projectId 培训项目主键
     * @return 培训项目
     */
    public ProTraining selectProTrainingByProjectId(Long projectId);

    /**
     * 查询培训项目列表
     * 
     * @param proTraining 培训项目
     * @return 培训项目集合
     */
    public List<ProTraining> selectProTrainingList(ProTraining proTraining);

    /**
     * 新增培训项目
     * 
     * @param proTraining 培训项目
     * @return 结果
     */
    public int insertProTraining(ProTraining proTraining);

    /**
     * 修改培训项目
     * 
     * @param proTraining 培训项目
     * @return 结果
     */
    public int updateProTraining(ProTraining proTraining);

    /**
     * 批量删除培训项目
     * 
     * @param projectIds 需要删除的培训项目主键集合
     * @return 结果
     */
    public int deleteProTrainingByProjectIds(Long[] projectIds);

    /**
     * 删除培训项目信息
     * 
     * @param projectId 培训项目主键
     * @return 结果
     */
    public int deleteProTrainingByProjectId(Long projectId);

    /**
     * 导入模式数据
     *
     * @param programList 专业数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param isOverwrite 是否覆盖数据，如果为true则先清空所有数据再导入
     * @param operName 操作用户
     * @return 结果
     */
    public String importProgram(List<ProTraining> programList, Boolean isUpdateSupport, Boolean isOverwrite, String operName);

}
