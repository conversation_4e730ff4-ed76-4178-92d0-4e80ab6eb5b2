package com.ruoyi.project.project.service.impl;

import java.util.List;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.project.mapper.ProTrainingMapper;
import com.ruoyi.project.project.domain.ProTraining;
import com.ruoyi.project.project.service.IProTrainingService;

/**
 * 培训项目Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
@Service
public class ProTrainingServiceImpl implements IProTrainingService 
{
    private static final Logger log = LoggerFactory.getLogger(ProTrainingServiceImpl.class);
    @Autowired
    private ProTrainingMapper proTrainingMapper;

    /**
     * 查询培训项目
     * 
     * @param projectId 培训项目主键
     * @return 培训项目
     */
    @Override
    public ProTraining selectProTrainingByProjectId(Long projectId)
    {
        return proTrainingMapper.selectProTrainingByProjectId(projectId);
    }

    /**
     * 查询培训项目列表
     * 
     * @param proTraining 培训项目
     * @return 培训项目
     */
    @Override
    public List<ProTraining> selectProTrainingList(ProTraining proTraining)
    {
        return proTrainingMapper.selectProTrainingList(proTraining);
    }

    /**
     * 新增培训项目
     * 
     * @param proTraining 培训项目
     * @return 结果
     */
    @Override
    public int insertProTraining(ProTraining proTraining)
    {
        proTraining.setCreateTime(DateUtils.getNowDate());
        return proTrainingMapper.insertProTraining(proTraining);
    }

    /**
     * 修改培训项目
     * 
     * @param proTraining 培训项目
     * @return 结果
     */
    @Override
    public int updateProTraining(ProTraining proTraining)
    {
        proTraining.setUpdateTime(DateUtils.getNowDate());
        return proTrainingMapper.updateProTraining(proTraining);
    }

    /**
     * 批量删除培训项目
     * 
     * @param projectIds 需要删除的培训项目主键
     * @return 结果
     */
    @Override
    public int deleteProTrainingByProjectIds(Long[] projectIds)
    {
        return proTrainingMapper.deleteProTrainingByProjectIds(projectIds);
    }

    /**
     * 删除培训项目信息
     * 
     * @param projectId 培训项目主键
     * @return 结果
     */
    @Override
    public int deleteProTrainingByProjectId(Long projectId)
    {
        return proTrainingMapper.deleteProTrainingByProjectId(projectId);
    }

    /**
     * 导入 training mode data
     *
     * @param trainingList training data list
     * @param isUpdateSupport whether to support update, if it exists, update the data
     * @param isOverwrite whether to overwrite data, if true, clear all data before importing
     * @param operName operator
     * @return result
     */
    @Override
    public String importProgram(List<ProTraining> trainingList, Boolean isUpdateSupport, Boolean isOverwrite, String operName)
    {
        if (StringUtils.isNull(trainingList) || trainingList.size() == 0)
        {
            throw new ServiceException("导入培训数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        // If you need to overwrite data, clear all data first
        if (isOverwrite)
        {
            proTrainingMapper.deleteAllProTraining();
        }

        for (ProTraining t : trainingList)
        {
            try
            {
                // Convert to ProTraining object
                ProTraining p = new ProTraining();
                p.setProjectName(t.getProjectName());
                p.setTrainingMode(t.getTrainingMode());
                p.setTrainingDays(t.getTrainingDays());
                p.setProjectStatus(t.getProjectStatus());
                p.setStartDate(t.getStartDate());
                p.setEndDate(t.getEndDate());
                p.setProjectDesc(t.getProjectDesc());

                p.setCreateBy(operName);
                p.setCreateTime(DateUtils.getNowDate());
                proTrainingMapper.insertProTraining(p);
                successNum++;
                successMsg.append("<br/>" + successNum + "、项目名称 " + p.getProjectName() + " 导入成功");
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、项目名称 " + t.getProjectName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
