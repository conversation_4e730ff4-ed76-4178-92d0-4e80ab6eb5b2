package com.ruoyi.project.project.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.project.domain.ProResource;
import com.ruoyi.project.project.service.IProResourceService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 资源Controller
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@RestController
@RequestMapping("/project/resource")
public class ProResourceController extends BaseController
{
    @Autowired
    private IProResourceService proResourceService;

    /**
     * 查询资源列表
     */
    @PreAuthorize("@ss.hasPermi('project:resource:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProResource proResource)
    {
        startPage();
        List<ProResource> list = proResourceService.selectProResourceList(proResource);
        return getDataTable(list);
    }

    /**
     * 导出资源列表
     */
    @PreAuthorize("@ss.hasPermi('project:resource:export')")
    @Log(title = "资源", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProResource proResource)
    {
        List<ProResource> list = proResourceService.selectProResourceList(proResource);
        ExcelUtil<ProResource> util = new ExcelUtil<ProResource>(ProResource.class);
        util.exportExcel(response, list, "资源数据");
    }

    /**
     * 获取资源详细信息
     */
    @PreAuthorize("@ss.hasPermi('project:resource:query')")
    @GetMapping(value = "/{resourceId}")
    public AjaxResult getInfo(@PathVariable("resourceId") Long resourceId)
    {
        return success(proResourceService.selectProResourceByResourceId(resourceId));
    }

    /**
     * 新增资源
     */
    @PreAuthorize("@ss.hasPermi('project:resource:add')")
    @Log(title = "资源", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProResource proResource)
    {
        return toAjax(proResourceService.insertProResource(proResource));
    }

    /**
     * 修改资源
     */
    @PreAuthorize("@ss.hasPermi('project:resource:edit')")
    @Log(title = "资源", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProResource proResource)
    {
        return toAjax(proResourceService.updateProResource(proResource));
    }

    /**
     * 删除资源
     */
    @PreAuthorize("@ss.hasPermi('project:resource:remove')")
    @Log(title = "资源", businessType = BusinessType.DELETE)
	@DeleteMapping("/{resourceIds}")
    public AjaxResult remove(@PathVariable Long[] resourceIds)
    {
        return toAjax(proResourceService.deleteProResourceByResourceIds(resourceIds));
    }
}
