package com.ruoyi.project.project.service;

import java.util.List;

import com.ruoyi.project.organization.domain.OrgUser;
import com.ruoyi.project.project.domain.ProMode;

/**
 * 培训模式Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
public interface IProModeService 
{
    /**
     * 查询培训模式
     * 
     * @param modeId 培训模式主键
     * @return 培训模式
     */
    public ProMode selectProModeByModeId(Long modeId);

    /**
     * 查询培训模式列表
     * 
     * @param proMode 培训模式
     * @return 培训模式集合
     */
    public List<ProMode> selectProModeList(ProMode proMode);

    /**
     * 新增培训模式
     * 
     * @param proMode 培训模式
     * @return 结果
     */
    public int insertProMode(ProMode proMode);

    /**
     * 修改培训模式
     * 
     * @param proMode 培训模式
     * @return 结果
     */
    public int updateProMode(ProMode proMode);

    /**
     * 批量删除培训模式
     * 
     * @param modeIds 需要删除的培训模式主键集合
     * @return 结果
     */
    public int deleteProModeByModeIds(Long[] modeIds);

    /**
     * 删除培训模式信息
     * 
     * @param modeId 培训模式主键
     * @return 结果
     */
    public int deleteProModeByModeId(Long modeId);
    /**
     * 导入模式数据
     *
     * @param programList 专业数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param isOverwrite 是否覆盖数据，如果为true则先清空所有数据再导入
     * @param operName 操作用户
     * @return 结果
     */
    public String importProgram(List<ProMode> programList, Boolean isUpdateSupport, Boolean isOverwrite, String operName);

}
