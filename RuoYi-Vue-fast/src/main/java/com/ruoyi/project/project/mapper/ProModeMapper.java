package com.ruoyi.project.project.mapper;

import java.util.List;
import com.ruoyi.project.project.domain.ProMode;

/**
 * 培训模式Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
public interface ProModeMapper 
{
    /**
     * 查询培训模式
     * 
     * @param modeId 培训模式主键
     * @return 培训模式
     */
    public ProMode selectProModeByModeId(Long modeId);

    /**
     * 查询培训模式列表
     * 
     * @param proMode 培训模式
     * @return 培训模式集合
     */
    public List<ProMode> selectProModeList(ProMode proMode);

    /**
     * 新增培训模式
     * 
     * @param proMode 培训模式
     * @return 结果
     */
    public int insertProMode(ProMode proMode);

    /**
     * 修改培训模式
     * 
     * @param proMode 培训模式
     * @return 结果
     */
    public int updateProMode(ProMode proMode);

    /**
     * 删除培训模式
     * 
     * @param modeId 培训模式主键
     * @return 结果
     */
    public int deleteProModeByModeId(Long modeId);

    /**
     * 批量删除培训模式
     * 
     * @param modeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProModeByModeIds(Long[] modeIds);
    /**
     * 清空所有培训模式
     *
     * @return 结果
     */
    public int deleteAllProMode();
}
