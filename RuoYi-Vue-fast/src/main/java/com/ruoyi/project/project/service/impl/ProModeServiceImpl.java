package com.ruoyi.project.project.service.impl;

import java.util.List;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.project.organization.service.impl.OrgUserServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.project.mapper.ProModeMapper;
import com.ruoyi.project.project.domain.ProMode;
import com.ruoyi.project.project.service.IProModeService;

/**
 * 培训模式Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
@Service
public class ProModeServiceImpl implements IProModeService 
{
    private static final Logger log = LoggerFactory.getLogger(ProModeServiceImpl.class);
    @Autowired
    private ProModeMapper proModeMapper;

    /**
     * 查询培训模式
     * 
     * @param modeId 培训模式主键
     * @return 培训模式
     */
    @Override
    public ProMode selectProModeByModeId(Long modeId)
    {
        return proModeMapper.selectProModeByModeId(modeId);
    }

    /**
     * 查询培训模式列表
     * 
     * @param proMode 培训模式
     * @return 培训模式
     */
    @Override
    public List<ProMode> selectProModeList(ProMode proMode)
    {
        return proModeMapper.selectProModeList(proMode);
    }

    /**
     * 新增培训模式
     * 
     * @param proMode 培训模式
     * @return 结果
     */
    @Override
    public int insertProMode(ProMode proMode)
    {
        proMode.setCreateTime(DateUtils.getNowDate());
        return proModeMapper.insertProMode(proMode);
    }

    /**
     * 修改培训模式
     * 
     * @param proMode 培训模式
     * @return 结果
     */
    @Override
    public int updateProMode(ProMode proMode)
    {
        proMode.setUpdateTime(DateUtils.getNowDate());
        return proModeMapper.updateProMode(proMode);
    }

    /**
     * 批量删除培训模式
     * 
     * @param modeIds 需要删除的培训模式主键
     * @return 结果
     */
    @Override
    public int deleteProModeByModeIds(Long[] modeIds)
    {
        return proModeMapper.deleteProModeByModeIds(modeIds);
    }

    /**
     * 删除培训模式信息
     * 
     * @param modeId 培训模式主键
     * @return 结果
     */
    @Override
    public int deleteProModeByModeId(Long modeId)
    {
        return proModeMapper.deleteProModeByModeId(modeId);
    }
    /**
     * 导入 training mode data
     *
     * @param modeList training mode data list
     * @param isUpdateSupport whether to support update, if it exists, update the data
     * @param isOverwrite whether to overwrite data, if true, clear all data before importing
     * @param operName operator
     * @return result
     */
    @Override
    public String importProgram(List<ProMode> modeList, Boolean isUpdateSupport, Boolean isOverwrite, String operName)
    {
        if (StringUtils.isNull(modeList) || modeList.size() == 0)
        {
            throw new ServiceException("导入培训模式数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        // If you need to overwrite data, clear all data first
        if (isOverwrite)
        {
            proModeMapper.deleteAllProMode();
        }

        for (ProMode mode : modeList)
        {
            try
            {
                // Convert to ProMode object
                ProMode p = new ProMode();
                p.setModeName(mode.getModeName());

                p.setCreateBy(operName);
                p.setCreateTime(DateUtils.getNowDate());
                proModeMapper.insertProMode(p);
                successNum++;
                successMsg.append("<br/>" + successNum + "、模式名称 " + p.getModeName() + " 导入成功");
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、模式名称 " + mode.getModeName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
