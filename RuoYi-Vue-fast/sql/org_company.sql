-- ----------------------------
-- 公司表
-- ----------------------------
drop table if exists `org_company`;
create table `org_company` (
  `company_id`      bigint(20)      not null auto_increment    comment '企业id',
  `company_name`    varchar(30)     default ''                 comment '企业名称',
  `social_code`     varchar(30)     default ''                 comment '统一社会信用代码',
  `legal_person`    varchar(20)     default ''                 comment '法人',
  `address`         varchar(64)     default ''                 comment '所在地址',
  `contact_person`  varchar(20)     default ''                 comment '联系人',
  `contact_info`    varchar(20)     default ''                 comment '联系方式',
  `job_count`       int(11)         default 0                  comment '提供岗位数',
  `signed_count`    int(11)         default 0                  comment '签约学生数',
  `create_by`       varchar(64)     default ''                 comment '创建者',
  `create_time` 	datetime                                   comment '创建时间',
  `update_by`       varchar(64)      default ''                comment '更新者',
  `update_time`     datetime                                   comment '更新时间',
  primary key (company_id)
) engine=innodb auto_increment=100 comment = '公司表';