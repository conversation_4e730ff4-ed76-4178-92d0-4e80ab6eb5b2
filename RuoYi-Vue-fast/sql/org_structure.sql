-- ----------------------------
-- 机构信息表
-- ----------------------------
drop table if exists `org_structure`;
create table `org_structure` (
  `structure_id`           bigint(20)      not null auto_increment  comment '机构ID',
  `structure_name`         varchar(30)     default ''               comment '机构名称',
  `parent_id`        bigint(20)      default 0                      comment '上级机构ID',
  `structure_type`         varchar(20)     default ''               comment '机构类型',
  `structure_abbreviation` varchar(20)     default ''               comment '机构简称',
  `director`         varchar(20)     default ''                     comment '负责人',
  `contact_info`     varchar(20)     default ''                     comment '联系方式',
  `description`      varchar(300)    default ''                     comment '简介',
  `create_by`        varchar(64)     default ''                     comment '创建者',
  `create_time`      datetime                                       comment '创建时间',
  `update_by`        varchar(64)     default ''                     comment '更新者',
  `update_time`      datetime                                       comment '更新时间',
  primary key (structure_id)
) engine=innodb auto_increment=100 comment = '机构信息表';