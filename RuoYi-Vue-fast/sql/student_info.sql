-- ----------------------------
-- 学生信息表
-- ----------------------------
drop table if exists student_info;
create table student_info (
  student_id           bigint(20)      not null auto_increment    comment '学生ID',
  student_name         varchar(50)     not null                   comment '学生姓名',
  student_number       varchar(50)     not null                   comment '学号',
  class_name           varchar(100)    default ''                 comment '班级',
  gender               char(1)         default '0'                comment '性别（0男 1女）',
  student_status       varchar(20)     default '0'                comment '学生状态（0未面试 1面试失败 2成功签约）',
  school_teacher       varchar(100)    default ''                 comment '校内教师',
  company_teacher      varchar(100)    default ''                 comment '企业导师',
  major                varchar(100)    default ''                 comment '专业',
  target_position      varchar(200)    default ''                 comment '目标岗位',
  resident_company     varchar(200)    default ''                 comment '入驻企业',
  salary               decimal(10,2)   default null               comment '薪酬',
  contract_id          bigint(20)      default null               comment '关联合同ID',
  position_competency_threshold int(11) default 2                 comment '岗位能力达标阈值',
  create_by            varchar(64)     default ''                 comment '创建者',
  create_time          datetime                                   comment '创建时间',
  update_by            varchar(64)     default ''                 comment '更新者',
  update_time          datetime                                   comment '更新时间',
  remark               varchar(500)    default null               comment '备注',
  primary key (student_id),
  unique key uk_student_number (student_number)
) engine=innodb auto_increment=1 comment = '学生信息表';

-- ----------------------------
-- 学生信息表数据请参考 test_data_init.sql 文件
-- ----------------------------