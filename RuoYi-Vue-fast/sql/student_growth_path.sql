-- ----------------------------
-- 学生成长路径表
-- ----------------------------
drop table if exists student_growth_path;
create table student_growth_path (
  path_id              bigint(20)      not null auto_increment    comment '路径ID',
  student_id           bigint(20)      not null                   comment '学生ID',
  semester_code        varchar(50)     not null                   comment '学期编码（如：2024-2025-1）',
  learning_situation   text                                       comment '学习情况描述',
  create_by            varchar(64)     default ''                 comment '创建者',
  create_time          datetime                                   comment '创建时间',
  update_by            varchar(64)     default ''                 comment '更新者',
  update_time          datetime                                   comment '更新时间',
  primary key (path_id),
  index idx_student_id (student_id),
  index idx_semester_code (semester_code)
) engine=innodb auto_increment=1 comment = '学生成长路径表';

-- ----------------------------
-- 学生成长路径数据请参考 test_data_init.sql 文件
-- ----------------------------