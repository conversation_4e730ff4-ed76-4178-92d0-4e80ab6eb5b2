/*
 人才管理系统 - 测试数据初始化脚本
 
 说明：
 1. 本文件包含所有业务表的测试数据
 2. 权限相关数据保持在 ry_simplified.sql 中
 3. 数据之间保持外键关联的完整性
 4. 模拟真实的业务场景
 
 Date: 2025-08-05
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 初始化企业数据
-- ----------------------------
INSERT INTO `org_company` VALUES 
(1, '华为技术有限公司', '91440300279174282F', '任正非', '深圳市龙岗区坂田华为基地', '张经理', '13800138001', 15, 8, 'admin', sysdate(), '', null),
(2, '腾讯科技有限公司', '91440300708461136T', '马化腾', '深圳市南山区科技园', '李经理', '13800138002', 12, 6, 'admin', sysdate(), '', null),
(3, '阿里巴巴集团', '91330100MA27XF6T8K', '张勇', '杭州市余杭区文一西路969号', '王经理', '13800138003', 20, 10, 'admin', sysdate(), '', null),
(4, '百度在线网络技术公司', '91110000802100433B', '李彦宏', '北京市海淀区上地十街10号', '赵经理', '13800138004', 8, 4, 'admin', sysdate(), '', null),
(5, '字节跳动科技有限公司', '91110108MA01GKU05N', '梁汝波', '北京市海淀区北三环西路甲18号', '刘经理', '13800138005', 18, 9, 'admin', sysdate(), '', null);

-- ----------------------------
-- 初始化专业数据
-- ----------------------------
INSERT INTO `org_major` VALUES 
(1, '计算机应用技术', '培养掌握计算机应用技术基本理论和技能的高素质技术技能人才', 'admin', sysdate(), '', null),
(2, '大数据技术', '培养掌握大数据采集、存储、处理与分析技术的专业人才', 'admin', sysdate(), '', null),
(3, '软件技术', '培养掌握软件开发、测试、维护等技术的专业人才', 'admin', sysdate(), '', null),
(4, '网络技术', '培养掌握网络规划、建设、管理与维护技术的专业人才', 'admin', sysdate(), '', null),
(5, '人工智能技术', '培养掌握人工智能算法、应用开发技术的专业人才', 'admin', sysdate(), '', null);

-- ----------------------------
-- 初始化教师数据
-- ----------------------------
INSERT INTO `org_teacher` VALUES 
(1, '葛延宇', '校内教师', '计算机学院', '', '副教授', '男', '13901234567', 'admin', sysdate(), '', null),
(2, '沈咏东', '校内教师', '计算机学院', '', '讲师', '男', '13901234568', 'admin', sysdate(), '', null),
(3, '孙旭', '校内教师', '计算机学院', '', '教授', '男', '13901234569', 'admin', sysdate(), '', null),
(4, '康佳炜', '校内教师', '计算机学院', '', '副教授', '女', '13901234570', 'admin', sysdate(), '', null),
(5, '申海元', '校内教师', '计算机学院', '', '讲师', '男', '13901234571', 'admin', sysdate(), '', null),
(6, '庄国强', '企业导师', '', '华为技术有限公司', '高级工程师', '男', '13801234567', 'admin', sysdate(), '', null),
(7, '荀磊', '企业导师', '', '腾讯科技有限公司', '技术专家', '男', '13801234568', 'admin', sysdate(), '', null),
(8, '王建华', '企业导师', '', '阿里巴巴集团', '资深工程师', '男', '13801234569', 'admin', sysdate(), '', null),
(9, '李明', '企业导师', '', '百度在线网络技术公司', '技术总监', '男', '13801234570', 'admin', sysdate(), '', null),
(10, '张伟', '企业导师', '', '字节跳动科技有限公司', '高级架构师', '男', '13801234571', 'admin', sysdate(), '', null);

-- ----------------------------
-- 初始化岗位模型数据（保持原有数据）
-- ----------------------------
INSERT INTO `post_model` VALUES 
(1, '机械设计师', '负责机械产品的设计和开发工作', 'admin', sysdate(), '', null, null),
(2, '电气技术员', '负责电气设备的维护和技术支持', 'admin', sysdate(), '', null, null),
(3, '软件工程师', '负责软件系统的开发和维护', 'admin', sysdate(), '', null, null),
(4, '数据分析师', '负责数据分析和业务洞察', 'admin', sysdate(), '', null, null),
(5, '质量检测员', '负责产品质量检测和控制', 'admin', sysdate(), '', null, null),
(6, 'Java开发工程师', '负责Java后端系统开发', 'admin', sysdate(), '', null, null),
(7, '前端开发工程师', '负责Web前端界面开发', 'admin', sysdate(), '', null, null),
(8, '测试工程师', '负责软件产品测试工作', 'admin', sysdate(), '', null, null),
(9, '产品经理', '负责产品规划和需求管理', 'admin', sysdate(), '', null, null),
(10, 'AI算法工程师', '负责人工智能算法研发', 'admin', sysdate(), '', null, null);

-- ----------------------------
-- 初始化岗位模型与企业关联数据
-- ----------------------------
INSERT INTO `post_model_company` VALUES 
(1, 1), (1, 2), (2, 2), (2, 3), (3, 1), (3, 2), (3, 3), (3, 4), (3, 5),
(4, 1), (4, 3), (4, 4), (5, 1), (5, 3), (5, 5),
(6, 1), (6, 2), (6, 3), (6, 4), (6, 5),
(7, 2), (7, 3), (7, 4), (7, 5),
(8, 1), (8, 2), (8, 3), (8, 4), (8, 5),
(9, 2), (9, 3), (9, 4), (9, 5),
(10, 1), (10, 3), (10, 4), (10, 5);

-- ----------------------------
-- 初始化职业能力数据（扩展原有数据）
-- ----------------------------
INSERT INTO `professional_ability` VALUES 
(1, '机械设计师', '具备机械产品设计和开发的专业能力', 60, 'admin', sysdate(), '', null, null),
(2, '电气技术员', '具备电气设备维护和技术支持的专业能力', 60, 'admin', sysdate(), '', null, null),
(3, '软件工程师', '具备软件系统开发和维护的专业能力', 60, 'admin', sysdate(), '', null, null),
(4, '数据分析师', '具备数据分析和业务洞察的专业能力', 60, 'admin', sysdate(), '', null, null),
(5, '质量检测员', '具备产品质量检测和控制的专业能力', 60, 'admin', sysdate(), '', null, null),
(6, 'Java开发能力', '具备Java后端开发的专业技能', 65, 'admin', sysdate(), '', null, null),
(7, '前端开发能力', '具备Web前端开发的专业技能', 65, 'admin', sysdate(), '', null, null),
(8, '软件测试能力', '具备软件测试设计和执行的专业技能', 60, 'admin', sysdate(), '', null, null),
(9, '产品设计能力', '具备产品规划和需求分析的专业技能', 70, 'admin', sysdate(), '', null, null),
(10, 'AI算法能力', '具备人工智能算法设计和实现的专业技能', 75, 'admin', sysdate(), '', null, null);

-- ----------------------------
-- 初始化学校课程数据
-- ----------------------------
INSERT INTO `course_school` VALUES 
(1, 'Java程序设计', 'Java语言基础语法、面向对象编程、集合框架等核心技术', 64, 4, '掌握Java编程基础，具备面向对象编程思维', 'admin', sysdate(), '', null),
(2, '数据库原理与应用', 'MySQL数据库设计、SQL语言、数据库优化等', 48, 3, '掌握数据库设计和SQL编程技能', 'admin', sysdate(), '', null),
(3, 'Web前端技术', 'HTML、CSS、JavaScript、Vue.js等前端技术', 64, 4, '掌握现代Web前端开发技术', 'admin', sysdate(), '', null),
(4, '软件工程', '软件开发生命周期、需求分析、系统设计等', 48, 3, '掌握软件工程基本理论和方法', 'admin', sysdate(), '', null),
(5, '数据结构与算法', '线性表、树、图等数据结构及相关算法', 64, 4, '掌握常用数据结构和算法设计', 'admin', sysdate(), '', null);

-- ----------------------------
-- 初始化企业课程数据
-- ----------------------------
INSERT INTO `course_company` VALUES 
(1, 'Spring Boot企业级开发', '华为技术有限公司', 'Spring Boot框架、微服务架构、企业级应用开发', 80, 5, '掌握企业级Java开发技术', 'admin', sysdate(), '', null),
(2, '大数据处理技术', '阿里巴巴集团', 'Hadoop、Spark、Flink等大数据处理技术', 96, 6, '掌握大数据处理和分析技术', 'admin', sysdate(), '', null),
(3, '移动应用开发', '腾讯科技有限公司', 'Android、iOS移动应用开发技术', 80, 5, '掌握移动应用开发技能', 'admin', sysdate(), '', null),
(4, '人工智能应用', '百度在线网络技术公司', '机器学习、深度学习、AI应用开发', 96, 6, '掌握AI技术在实际项目中的应用', 'admin', sysdate(), '', null),
(5, '云计算技术', '字节跳动科技有限公司', '云服务、容器技术、DevOps等', 80, 5, '掌握云计算和DevOps技术', 'admin', sysdate(), '', null);

-- ----------------------------
-- 初始化学生合同信息数据（扩展原有数据）
-- ----------------------------
INSERT INTO `student_contract` VALUES
(1, '20240301001', '校企合作培训合同', '2024-03-01', 1, '华为技术有限公司', 'Java开发工程师', 8000.00, 'admin', sysdate(), '', null, ''),
(2, '20240301002', '实习培训合同', '2024-03-02', 2, '腾讯科技有限公司', '前端开发工程师', 7500.00, 'admin', sysdate(), '', null, ''),
(3, '20240301003', '就业培训合同', '2024-03-03', 3, '阿里巴巴集团', '数据分析师', 9000.00, 'admin', sysdate(), '', null, ''),
(4, '20240301004', '校企合作培训合同', '2024-03-04', 4, '百度在线网络技术公司', 'AI算法工程师', 10000.00, 'admin', sysdate(), '', null, ''),
(5, '20240301005', '实习培训合同', '2024-03-05', 5, '字节跳动科技有限公司', '测试工程师', 7000.00, 'admin', sysdate(), '', null, ''),
(6, '20240301006', '校企合作培训合同', '2024-03-06', 6, '华为技术有限公司', '软件工程师', 8500.00, 'admin', sysdate(), '', null, ''),
(7, '20240301007', '就业培训合同', '2024-03-07', 7, '腾讯科技有限公司', '产品经理', 9500.00, 'admin', sysdate(), '', null, ''),
(8, '20240301008', '实习培训合同', '2024-03-08', 8, '阿里巴巴集团', 'Java开发工程师', 8200.00, 'admin', sysdate(), '', null, ''),
(9, '20240301009', '校企合作培训合同', '2024-03-09', 9, '百度在线网络技术公司', '数据分析师', 8800.00, 'admin', sysdate(), '', null, ''),
(10, '20240301010', '就业培训合同', '2024-03-10', 10, '字节跳动科技有限公司', '前端开发工程师', 7800.00, 'admin', sysdate(), '', null, '');

-- ----------------------------
-- 初始化学生信息数据（扩展原有数据，保证关联完整性）
-- ----------------------------
INSERT INTO `student_info` VALUES
(1, '王尔康', '2450301537', '24计应五', '0', '2', '葛延宇', '庄国强', '计算机应用技术', 'Java开发工程师', '华为技术有限公司', 8000.00, 1, 2, 'admin', sysdate(), '', null, '优秀学生'),
(2, '丁豪谷', '2450301538', '24计应五', '0', '2', '沈咏东', '荀磊', '计算机应用技术', '前端开发工程师', '腾讯科技有限公司', 7500.00, 2, 2, 'admin', sysdate(), '', null, ''),
(3, '胡允', '2450301539', '24计应五', '0', '2', '孙旭', '王建华', '计算机应用技术', '数据分析师', '阿里巴巴集团', 9000.00, 3, 2, 'admin', sysdate(), '', null, ''),
(4, '李书君', '2450301540', '24计应五', '0', '2', '康佳炜', '李明', '计算机应用技术', 'AI算法工程师', '百度在线网络技术公司', 10000.00, 4, 2, 'admin', sysdate(), '', null, ''),
(5, '段向懿', '2450301541', '24计应五', '0', '2', '申海元', '张伟', '计算机应用技术', '测试工程师', '字节跳动科技有限公司', 7000.00, 5, 2, 'admin', sysdate(), '', null, ''),
(6, '赵雨', '2450401234', '24大数据二', '0', '2', '葛延宇', '庄国强', '大数据技术', '软件工程师', '华为技术有限公司', 8500.00, 6, 2, 'admin', sysdate(), '', null, ''),
(7, '李怡成', '2450401235', '24大数据二', '0', '2', '沈咏东', '荀磊', '大数据技术', '产品经理', '腾讯科技有限公司', 9500.00, 7, 2, 'admin', sysdate(), '', null, ''),
(8, '傅彭得', '2450401236', '24大数据二', '0', '2', '孙旭', '王建华', '大数据技术', 'Java开发工程师', '阿里巴巴集团', 8200.00, 8, 2, 'admin', sysdate(), '', null, ''),
(9, '刘大大', '2450401238', '24大数据二', '0', '2', '康佳炜', '李明', '大数据技术', '数据分析师', '百度在线网络技术公司', 8800.00, 9, 2, 'admin', sysdate(), '', null, ''),
(10, '杨静云', '2450401326', '24大数据二', '1', '2', '申海元', '张伟', '大数据技术', '前端开发工程师', '字节跳动科技有限公司', 7800.00, 10, 2, 'admin', sysdate(), '', null, ''),
(11, '张明', '2450501001', '24软件一', '0', '1', '葛延宇', '庄国强', '软件技术', 'Java开发工程师', '', null, null, 2, 'admin', sysdate(), '', null, '面试中'),
(12, '李华', '2450501002', '24软件一', '1', '0', '沈咏东', '', '软件技术', '', '', null, null, 2, 'admin', sysdate(), '', null, '待面试'),
(13, '王芳', '2450501003', '24软件一', '1', '1', '孙旭', '荀磊', '软件技术', '前端开发工程师', '', null, null, 2, 'admin', sysdate(), '', null, '面试失败'),
(14, '陈强', '2450501004', '24软件一', '0', '0', '康佳炜', '', '软件技术', '', '', null, null, 2, 'admin', sysdate(), '', null, '待面试'),
(15, '刘敏', '2450501005', '24软件一', '1', '0', '申海元', '', '软件技术', '', '', null, null, 2, 'admin', sysdate(), '', null, '待面试');

-- ----------------------------
-- 初始化培训方案数据（保持原有数据）
-- ----------------------------
INSERT INTO `training_program` VALUES
(1, '机械设计师', '培养具备机械产品设计和开发能力的专业人才', 3, 30, '培养具备机械产品设计和开发能力的专业人才，能够独立完成机械产品的设计、分析和优化工作。', '掌握机械设计基础理论，熟练使用CAD/CAM软件，具备产品设计和开发能力。', 0, 'admin', sysdate(), '', null, null),
(2, '电气技术员', '培养具备电气设备维护和技术支持能力的专业人才', 2, 25, '培养具备电气设备维护和技术支持能力的专业人才，能够独立完成电气设备的安装、调试和维护工作。', '掌握电气技术基础理论，熟练使用电气设计软件，具备设备维护和技术支持能力。', 0, 'admin', sysdate(), '', null, null),
(3, '软件工程师', '培养具备软件系统开发和维护能力的专业人才', 3, 40, '培养具备软件系统开发和维护能力的专业人才，能够独立完成软件系统的设计、开发和维护工作。', '掌握软件工程基础理论，熟练使用多种编程语言和开发工具，具备系统开发和维护能力。', 0, 'admin', sysdate(), '', null, null),
(4, '数据分析师', '培养具备数据分析和业务洞察能力的专业人才', 2, 20, '培养具备数据分析和业务洞察能力的专业人才，能够独立完成数据分析和业务洞察工作。', '掌握数据分析基础理论，熟练使用数据分析工具，具备数据分析和业务洞察能力。', 0, 'admin', sysdate(), '', null, null),
(5, '质量检测员', '培养具备产品质量检测和控制能力的专业人才', 1, 15, '培养具备产品质量检测和控制能力的专业人才，能够独立完成产品质量检测和控制工作。', '掌握质量检测基础理论，熟练使用检测设备和工具，具备质量检测和控制能力。', 0, 'admin', sysdate(), '', null, null),
(6, 'Java全栈开发工程师', '培养具备Java全栈开发能力的专业人才', 3, 50, '培养具备Java全栈开发能力的专业人才，能够独立完成企业级Java应用的设计、开发和部署工作。', '掌握Java核心技术、Spring生态、前端技术、数据库技术，具备全栈开发能力。', 0, 'admin', sysdate(), '', null, null),
(7, '大数据开发工程师', '培养具备大数据处理和分析能力的专业人才', 2, 30, '培养具备大数据处理和分析能力的专业人才，能够独立完成大数据平台搭建和数据分析工作。', '掌握Hadoop、Spark等大数据技术，具备数据处理和分析能力。', 0, 'admin', sysdate(), '', null, null),
(8, 'AI算法工程师', '培养具备人工智能算法开发能力的专业人才', 3, 25, '培养具备人工智能算法开发能力的专业人才，能够独立完成AI算法设计和应用开发工作。', '掌握机器学习、深度学习理论，具备AI算法开发和应用能力。', 0, 'admin', sysdate(), '', null, null);

-- ----------------------------
-- 初始化评价题库数据（保持原有数据）
-- ----------------------------
INSERT INTO `evaluation_question_bank` VALUES
(1, '电子信息', '0', 'admin', sysdate(), '', null, '电子信息相关题目'),
(2, '土木工程', '0', 'admin', sysdate(), '', null, '土木工程相关题目'),
(3, '化工工艺', '0', 'admin', sysdate(), '', null, '化工工艺相关题目'),
(4, '能源动力', '0', 'admin', sysdate(), '', null, '能源动力相关题目'),
(5, '材料科学', '0', 'admin', sysdate(), '', null, '材料科学相关题目'),
(6, '生物工程', '0', 'admin', sysdate(), '', null, '生物工程相关题目'),
(7, '环境科学', '0', 'admin', sysdate(), '', null, '环境科学相关题目'),
(8, '测绘工程', '0', 'admin', sysdate(), '', null, '测绘工程相关题目'),
(9, '计算机技术', '0', 'admin', sysdate(), '', null, '计算机技术相关题目'),
(10, '软件工程', '0', 'admin', sysdate(), '', null, '软件工程相关题目');

-- ----------------------------
-- 初始化评价题目数据
-- ----------------------------
INSERT INTO `evaluation_question` VALUES
(1, 1, '在Java中，以下哪个关键字用于定义常量？', '0', '["A. final", "B. static", "C. const", "D. var"]', 3, 3, '0', 'admin', sysdate(), '', null, ''),
(2, 1, '以下哪些是Java的基本数据类型？', '1', '["A. int", "B. String", "C. boolean", "D. char"]', 3, 3, '0', 'admin', sysdate(), '', null, ''),
(3, 9, 'Spring Boot的主要优势包括哪些？', '1', '["A. 自动配置", "B. 内嵌服务器", "C. 简化依赖管理", "D. 提供监控功能"]', 6, 6, '0', 'admin', sysdate(), '', null, ''),
(4, 10, '软件测试的主要类型包括？', '1', '["A. 单元测试", "B. 集成测试", "C. 系统测试", "D. 验收测试"]', 8, 8, '0', 'admin', sysdate(), '', null, ''),
(5, 9, 'MySQL数据库优化的方法有哪些？', '1', '["A. 索引优化", "B. 查询优化", "C. 表结构优化", "D. 硬件优化"]', 4, 4, '0', 'admin', sysdate(), '', null, '');

-- ----------------------------
-- 初始化评价记录数据
-- ----------------------------
INSERT INTO `evaluation_record` VALUES
(1, '2024-12-01 Java基础测试', 1, 1, '电子信息', 5, 5, '1', '2024-12-01 09:00:00', '2024-12-01 09:30:00', 'admin', sysdate(), '', null, ''),
(2, '2024-12-02 Spring Boot测试', 2, 9, '计算机技术', 3, 3, '1', '2024-12-02 10:00:00', '2024-12-02 10:25:00', 'admin', sysdate(), '', null, ''),
(3, '2024-12-03 软件测试理论', 3, 10, '软件工程', 4, 4, '1', '2024-12-03 14:00:00', '2024-12-03 14:20:00', 'admin', sysdate(), '', null, ''),
(4, '2024-12-04 数据库优化', 4, 9, '计算机技术', 5, 3, '0', '2024-12-04 15:00:00', null, 'admin', sysdate(), '', null, '进行中'),
(5, '2024-12-05 综合能力测试', 5, 1, '电子信息', 10, 8, '0', '2024-12-05 16:00:00', null, 'admin', sysdate(), '', null, '进行中');

-- ----------------------------
-- 初始化评价记录答题数据
-- ----------------------------
INSERT INTO `evaluation_record_answer` VALUES
(1, 1, 1, '在Java中，以下哪个关键字用于定义常量？', '0', '["A. final", "B. static", "C. const", "D. var"]', 'A', '2024-12-01 09:05:00', 'admin', sysdate(), '', null),
(2, 1, 2, '以下哪些是Java的基本数据类型？', '1', '["A. int", "B. String", "C. boolean", "D. char"]', 'A,C,D', '2024-12-01 09:10:00', 'admin', sysdate(), '', null),
(3, 2, 3, 'Spring Boot的主要优势包括哪些？', '1', '["A. 自动配置", "B. 内嵌服务器", "C. 简化依赖管理", "D. 提供监控功能"]', 'A,B,C,D', '2024-12-02 10:10:00', 'admin', sysdate(), '', null),
(4, 3, 4, '软件测试的主要类型包括？', '1', '["A. 单元测试", "B. 集成测试", "C. 系统测试", "D. 验收测试"]', 'A,B,C,D', '2024-12-03 14:10:00', 'admin', sysdate(), '', null);

-- ----------------------------
-- 初始化学生考试成绩数据
-- ----------------------------
INSERT INTO `student_exam_score` VALUES
(1, 1, 'Java程序设计', '0', '期末考试', 92.0, 'admin', sysdate(), '', null, '优秀'),
(2, 1, '数据库原理与应用', '0', '期中考试', 88.5, 'admin', sysdate(), '', null, ''),
(3, 1, 'Spring Boot企业级开发', '1', '项目答辩', 95.0, 'admin', sysdate(), '', null, '企业课程'),
(4, 2, 'Web前端技术', '0', '期末考试', 85.0, 'admin', sysdate(), '', null, ''),
(5, 2, '移动应用开发', '1', '项目实训', 90.0, 'admin', sysdate(), '', null, ''),
(6, 3, '数据结构与算法', '0', '期末考试', 91.5, 'admin', sysdate(), '', null, ''),
(7, 3, '大数据处理技术', '1', '综合项目', 93.0, 'admin', sysdate(), '', null, ''),
(8, 4, '软件工程', '0', '期末考试', 87.0, 'admin', sysdate(), '', null, ''),
(9, 4, '人工智能应用', '1', '算法实现', 96.0, 'admin', sysdate(), '', null, ''),
(10, 5, 'Java程序设计', '0', '期中考试', 82.5, 'admin', sysdate(), '', null, ''),
(11, 6, '数据库原理与应用', '0', '期末考试', 89.0, 'admin', sysdate(), '', null, ''),
(12, 7, 'Web前端技术', '0', '期末考试', 94.5, 'admin', sysdate(), '', null, ''),
(13, 8, '软件工程', '0', '期末考试', 86.0, 'admin', sysdate(), '', null, ''),
(14, 9, '数据结构与算法', '0', '期末考试', 90.5, 'admin', sysdate(), '', null, ''),
(15, 10, 'Web前端技术', '0', '期末考试', 88.0, 'admin', sysdate(), '', null, '');

-- ----------------------------
-- 初始化学生成长路径数据
-- ----------------------------
INSERT INTO `student_growth_path` VALUES
(1, 1, '2024-2025-1', '第一学期表现优秀，Java基础扎实，积极参与课堂讨论，完成了个人博客项目开发', 'admin', sysdate(), '', null),
(2, 1, '2024-2025-2', '第二学期进入企业实训阶段，参与Spring Boot项目开发，技术能力显著提升', 'admin', sysdate(), '', null),
(3, 2, '2024-2025-1', '前端技术学习进展良好，掌握了Vue.js框架，完成了电商网站前端开发', 'admin', sysdate(), '', null),
(4, 3, '2024-2025-1', '数据分析能力突出，熟练使用Python进行数据处理，参与了企业数据分析项目', 'admin', sysdate(), '', null),
(5, 4, '2024-2025-1', 'AI算法学习成果显著，掌握了机器学习基础理论，实现了图像识别小项目', 'admin', sysdate(), '', null),
(6, 5, '2024-2025-1', '软件测试理论扎实，熟练使用自动化测试工具，参与了企业项目测试工作', 'admin', sysdate(), '', null),
(7, 6, '2024-2025-1', '全栈开发能力强，前后端技术均衡发展，独立完成了在线学习平台开发', 'admin', sysdate(), '', null),
(8, 7, '2024-2025-1', '产品思维敏锐，具备良好的需求分析能力，参与了企业产品规划工作', 'admin', sysdate(), '', null),
(9, 8, '2024-2025-1', 'Java开发技能扎实，代码质量高，团队协作能力强', 'admin', sysdate(), '', null),
(10, 9, '2024-2025-1', '数据分析思维清晰，能够从业务角度分析数据，提供有价值的洞察', 'admin', sysdate(), '', null);

-- ----------------------------
-- 初始化培训项目数据
-- ----------------------------
INSERT INTO `pro_training` VALUES
(1, 'Java全栈开发训练营', '项目驱动式培训', 180, '进行中', '2024-09-01 08:00:00', '2025-01-31 18:00:00', '面向计算机相关专业学生的Java全栈开发培训项目，包含前端、后端、数据库等全栈技术', 'admin', sysdate(), '', null),
(2, '大数据分析实训', '企业实战培训', 120, '进行中', '2024-10-01 08:00:00', '2025-02-28 18:00:00', '结合企业真实数据进行大数据处理和分析技能培训', 'admin', sysdate(), '', null),
(3, 'AI算法工程师培养', '理论+实践培训', 200, '筹备中', '2025-03-01 08:00:00', '2025-08-31 18:00:00', '人工智能算法理论学习与实际项目开发相结合的培训项目', 'admin', sysdate(), '', null),
(4, '移动应用开发训练', '项目实战培训', 90, '已完成', '2024-06-01 08:00:00', '2024-08-31 18:00:00', 'Android和iOS移动应用开发技能培训', 'admin', sysdate(), '', null),
(5, '软件测试工程师培养', '理论+工具培训', 60, '进行中', '2024-11-01 08:00:00', '2024-12-31 18:00:00', '软件测试理论、测试工具使用和自动化测试技能培训', 'admin', sysdate(), '', null);

-- ----------------------------
-- 初始化培训资源数据
-- ----------------------------
INSERT INTO `pro_resource` VALUES
(1, 'Java核心技术教程', '教材', '/resources/java-core-tutorial.pdf', 'admin', sysdate(), '', null),
(2, 'Spring Boot实战指南', '教材', '/resources/springboot-guide.pdf', 'admin', sysdate(), '', null),
(3, 'MySQL数据库设计规范', '资料', '/resources/mysql-design-standard.doc', 'admin', sysdate(), '', null),
(4, 'Vue.js开发环境配置', '软件', '/resources/vuejs-setup.zip', 'admin', sysdate(), '', null),
(5, '项目开发服务器', '设备', '*************', 'admin', sysdate(), '', null),
(6, 'IntelliJ IDEA开发工具', '软件', '/resources/idea-setup.exe', 'admin', sysdate(), '', null),
(7, '大数据处理平台搭建指南', '课件', '/resources/bigdata-platform.pptx', 'admin', sysdate(), '', null),
(8, 'Python数据分析库使用手册', '资料', '/resources/python-data-analysis.pdf', 'admin', sysdate(), '', null),
(9, '机器学习算法实现代码', '资料', '/resources/ml-algorithms.zip', 'admin', sysdate(), '', null),
(10, '软件测试工具集合', '软件', '/resources/testing-tools.zip', 'admin', sysdate(), '', null);

-- ----------------------------
-- 初始化培训模式数据
-- ----------------------------
INSERT INTO `pro_mode` VALUES
(1, '项目驱动式培训', 'admin', sysdate(), '', null),
(2, '企业实战培训', 'admin', sysdate(), '', null),
(3, '理论+实践培训', 'admin', sysdate(), '', null),
(4, '导师制培训', 'admin', sysdate(), '', null),
(5, '在线+线下混合培训', 'admin', sysdate(), '', null),
(6, '小组协作培训', 'admin', sysdate(), '', null),
(7, '个性化定制培训', 'admin', sysdate(), '', null);

-- ----------------------------
-- 初始化课程标准数据
-- ----------------------------
INSERT INTO `std_course` VALUES
(1, 'Java程序设计', 'Java语言基础语法、面向对象编程、异常处理、集合框架等核心技术的教学标准', '学生能够熟练掌握Java编程语言，具备面向对象编程思维，能够开发中小型Java应用程序', 'admin', sysdate(), '', null),
(2, '数据库原理与应用', 'MySQL数据库设计、SQL语言、存储过程、触发器、数据库优化等内容的教学标准', '学生能够设计和实现关系型数据库，熟练编写SQL语句，具备数据库管理和优化能力', 'admin', sysdate(), '', null),
(3, 'Web前端技术', 'HTML5、CSS3、JavaScript、Vue.js、响应式设计等前端技术的教学标准', '学生能够开发现代化的Web前端界面，掌握主流前端框架，具备用户体验设计思维', 'admin', sysdate(), '', null),
(4, '软件工程', '软件开发生命周期、需求分析、系统设计、项目管理、软件测试等内容的教学标准', '学生能够运用软件工程理论指导实践，具备软件项目管理和团队协作能力', 'admin', sysdate(), '', null),
(5, '数据结构与算法', '线性表、栈、队列、树、图等数据结构及相关算法的教学标准', '学生能够选择合适的数据结构解决实际问题，具备算法设计和分析能力', 'admin', sysdate(), '', null);

-- ----------------------------
-- 初始化制度管理数据
-- ----------------------------
INSERT INTO `std_regulation` VALUES
(1, '学生实习管理制度', '/regulations/student-internship-regulation.pdf', 'admin', sysdate(), '', null),
(2, '校企合作管理办法', '/regulations/school-enterprise-cooperation.pdf', 'admin', sysdate(), '', null),
(3, '教师企业实践管理规定', '/regulations/teacher-enterprise-practice.pdf', 'admin', sysdate(), '', null),
(4, '学生考核评价制度', '/regulations/student-assessment-system.pdf', 'admin', sysdate(), '', null),
(5, '培训项目质量管理制度', '/regulations/training-quality-management.pdf', 'admin', sysdate(), '', null),
(6, '实训基地建设标准', '/regulations/training-base-standards.pdf', 'admin', sysdate(), '', null),
(7, '学生安全管理制度', '/regulations/student-safety-management.pdf', 'admin', sysdate(), '', null);

-- ----------------------------
-- 初始化组织架构数据
-- ----------------------------
INSERT INTO `org_structure` VALUES
(1, '华信学院', 0, '学校', '华信', '张院长', '13900000001', '华信学院是一所以信息技术为特色的高等院校', 'admin', sysdate(), '', null),
(2, '计算机学院', 1, '学院', '计院', '李院长', '13900000002', '计算机学院下设多个专业方向', 'admin', sysdate(), '', null),
(3, '软件工程系', 2, '系部', '软工系', '王主任', '13900000003', '软件工程系负责软件相关专业教学', 'admin', sysdate(), '', null),
(4, '计算机应用系', 2, '系部', '计应系', '赵主任', '13900000004', '计算机应用系负责计算机应用技术专业', 'admin', sysdate(), '', null),
(5, '大数据系', 2, '系部', '大数据系', '刘主任', '13900000005', '大数据系负责大数据技术专业教学', 'admin', sysdate(), '', null),
(6, '教务处', 1, '教务处', '教务处', '陈处长', '13900000006', '负责全院教学管理工作', 'admin', sysdate(), '', null),
(7, '学生处', 1, '学生处', '学生处', '孙处长', '13900000007', '负责学生管理和服务工作', 'admin', sysdate(), '', null);

-- ----------------------------
-- 初始化组织人员数据
-- ----------------------------
INSERT INTO `org_user` VALUES
(1, '张院长', '华信学院', '院长', '13900000001', '男', '在职', 'admin', sysdate(), '', null),
(2, '李院长', '计算机学院', '院长', '13900000002', '男', '在职', 'admin', sysdate(), '', null),
(3, '王主任', '软件工程系', '系主任', '13900000003', '男', '在职', 'admin', sysdate(), '', null),
(4, '赵主任', '计算机应用系', '系主任', '女', '13900000004', '在职', 'admin', sysdate(), '', null),
(5, '刘主任', '大数据系', '系主任', '13900000005', '男', '在职', 'admin', sysdate(), '', null),
(6, '陈处长', '教务处', '处长', '13900000006', '女', '在职', 'admin', sysdate(), '', null),
(7, '孙处长', '学生处', '处长', '13900000007', '男', '在职', 'admin', sysdate(), '', null),
(8, '周老师', '软件工程系', '教研室主任', '13900000008', '女', '在职', 'admin', sysdate(), '', null),
(9, '吴老师', '计算机应用系', '教研室主任', '13900000009', '男', '在职', 'admin', sysdate(), '', null),
(10, '郑老师', '大数据系', '教研室主任', '13900000010', '女', '在职', 'admin', sysdate(), '', null);

-- ----------------------------
-- 初始化岗位模型配置数据
-- ----------------------------
INSERT INTO `post_model_config` VALUES
(1, 1, '机械设计师', 'admin', sysdate(), '', null, '机械设计师岗位配置'),
(2, 2, '电气技术员', 'admin', sysdate(), '', null, '电气技术员岗位配置'),
(3, 3, '软件工程师', 'admin', sysdate(), '', null, '软件工程师岗位配置'),
(4, 4, '数据分析师', 'admin', sysdate(), '', null, '数据分析师岗位配置'),
(5, 5, '质量检测员', 'admin', sysdate(), '', null, '质量检测员岗位配置'),
(6, 6, 'Java开发工程师', 'admin', sysdate(), '', null, 'Java开发工程师岗位配置'),
(7, 7, '前端开发工程师', 'admin', sysdate(), '', null, '前端开发工程师岗位配置'),
(8, 8, '测试工程师', 'admin', sysdate(), '', null, '测试工程师岗位配置'),
(9, 9, '产品经理', 'admin', sysdate(), '', null, '产品经理岗位配置'),
(10, 10, 'AI算法工程师', 'admin', sysdate(), '', null, 'AI算法工程师岗位配置');

-- ----------------------------
-- 初始化岗位职业能力配置数据
-- ----------------------------
INSERT INTO `post_ability_config` VALUES
(1, 3, 3, '软件工程师', 70, 100, 'admin', sysdate(), '', null, '软件工程师核心能力'),
(2, 6, 6, 'Java开发能力', 75, 100, 'admin', sysdate(), '', null, 'Java开发核心技能'),
(3, 7, 7, '前端开发能力', 70, 100, 'admin', sysdate(), '', null, '前端开发核心技能'),
(4, 8, 8, '软件测试能力', 65, 100, 'admin', sysdate(), '', null, '软件测试核心技能'),
(5, 9, 9, '产品设计能力', 75, 100, 'admin', sysdate(), '', null, '产品设计核心技能'),
(6, 10, 10, 'AI算法能力', 80, 100, 'admin', sysdate(), '', null, 'AI算法核心技能'),
(7, 4, 4, '数据分析师', 70, 100, 'admin', sysdate(), '', null, '数据分析核心能力'),
(8, 6, 3, '软件工程师', 60, 80, 'admin', sysdate(), '', null, 'Java开发辅助能力'),
(9, 7, 3, '软件工程师', 50, 60, 'admin', sysdate(), '', null, '前端开发辅助能力'),
(10, 8, 3, '软件工程师', 40, 50, 'admin', sysdate(), '', null, '测试技能辅助能力');

-- ----------------------------
-- 初始化岗位课程配置数据
-- ----------------------------
INSERT INTO `post_course_config` VALUES
(1, 6, 2, 1, 'Java程序设计', '掌握Java编程基础', 1, 100, 'admin', sysdate(), '', null, 'Java开发核心课程'),
(2, 6, 2, 2, '数据库原理与应用', '掌握数据库设计和SQL', 1, 80, 'admin', sysdate(), '', null, 'Java开发必修课程'),
(3, 6, 2, 1, 'Spring Boot企业级开发', '掌握企业级开发技术', 2, 100, 'admin', sysdate(), '', null, 'Java开发企业课程'),
(4, 7, 3, 3, 'Web前端技术', '掌握前端开发技术', 1, 100, 'admin', sysdate(), '', null, '前端开发核心课程'),
(5, 7, 3, 3, '移动应用开发', '掌握移动端开发', 2, 90, 'admin', sysdate(), '', null, '前端开发企业课程'),
(6, 8, 4, 4, '软件工程', '掌握软件测试理论', 1, 100, 'admin', sysdate(), '', null, '测试工程师核心课程'),
(7, 10, 6, 4, '人工智能应用', '掌握AI算法应用', 2, 100, 'admin', sysdate(), '', null, 'AI工程师企业课程'),
(8, 4, 7, 5, '数据结构与算法', '掌握数据分析基础', 1, 80, 'admin', sysdate(), '', null, '数据分析师基础课程'),
(9, 4, 7, 2, '大数据处理技术', '掌握大数据处理技术', 2, 100, 'admin', sysdate(), '', null, '数据分析师企业课程'),
(10, 9, 5, 4, '软件工程', '掌握产品设计理论', 1, 90, 'admin', sysdate(), '', null, '产品经理基础课程');

SET FOREIGN_KEY_CHECKS = 1;
