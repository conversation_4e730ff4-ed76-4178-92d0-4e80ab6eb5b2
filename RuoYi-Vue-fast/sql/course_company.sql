drop table if exists `course_company`;
create table `course_company` (
  `course_id`         bigint(20)      not null auto_increment    comment '课程id',
  `course_name`       varchar(20)     default ''                 comment '课程名称',
  `course_standard`   varchar(600)    default ''                 comment '课程标准',
  `course_hours`      int(11)         default 0                  comment '课程学时',
  `course_credit`     int(11)         default 0                  comment '课程学分',
  `course_goal`       varchar(600)    default ''                 comment '课程目标',
  `create_by`         varchar(64)     default ''                 comment '创建者',
  `create_time`       datetime                                   comment '创建时间',
  `update_by`         varchar(64)     default ''                 comment '更新者',
  `update_time`       datetime                                   comment '更新时间',
  primary key (course_id)
) engine=innodb auto_increment=100 comment = '企业课程表';