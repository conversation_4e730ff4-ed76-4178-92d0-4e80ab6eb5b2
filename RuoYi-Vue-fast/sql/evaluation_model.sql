-- ----------------------------
-- 评价模型题库表
-- ----------------------------
DROP TABLE IF EXISTS `evaluation_question_bank`;
CREATE TABLE `evaluation_question_bank` (
  `bank_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '题库ID',
  `bank_name` varchar(60) NOT NULL COMMENT '题库名称',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`bank_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='评价模型题库表';

-- ----------------------------
-- 评价模型数据请参考 test_data_init.sql 文件
-- ----------------------------

-- ----------------------------
-- 评价模型题目表
-- ----------------------------
DROP TABLE IF EXISTS `evaluation_question`;
CREATE TABLE `evaluation_question` (
  `question_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '题目ID',
  `bank_id` bigint(20) NOT NULL COMMENT '题库ID',
  `question_stem` varchar(500) NOT NULL COMMENT '题干',
  `question_type` char(1) NOT NULL COMMENT '题目类型（0单选题 1多选题）',
  `options` text NOT NULL COMMENT '选项（JSON格式）',
  `post_id` bigint(20) DEFAULT NULL COMMENT '岗位ID',
  `ability_id` bigint(20) DEFAULT NULL COMMENT '职业能力ID',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`question_id`),
  KEY `idx_bank_id` (`bank_id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_ability_id` (`ability_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='评价模型题目表';

