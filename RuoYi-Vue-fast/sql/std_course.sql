drop table if exists `std_course`;
create table `std_course` (
  `course_id`         bigint(20)      not null auto_increment    comment '课程id',
  `course_name`       varchar(100)    default ''                 comment '课程名称',
  `standard_desc`     varchar(600)    default ''                 comment '标准描述',
  `training_spec`     varchar(600)    default ''                 comment '培养规格',
  `create_by`         varchar(64)     default ''                 comment '创建者',
  `create_time`       datetime                                   comment '创建时间',
  `update_by`         varchar(64)     default ''                 comment '更新者',
  `update_time`       datetime                                   comment '更新时间',
  primary key (course_id)
) engine=innodb auto_increment=100 comment = '课程表';