drop table if exists `org_user`;
create table `org_user` (
  `user_id`          bigint(20)       not null auto_increment comment '人员id',
  `user_name`        varchar(30)      default ''              comment '人员姓名',
  `org_name`         varchar(30)      default null             comment '所属机构',
  `position`         varchar(30)     default ''               comment '职务',
  `contact_info`     varchar(20)     default ''               comment '联系方式',
  `gender`           char(1)         default ''               comment '性别（男/女）',
  `work_status`      varchar(20)     default ''               comment '在职状态（在职/离职）',
  `create_by`        varchar(64)     default ''               comment '创建者',
  `create_time`      datetime                                 comment '创建时间',
  `update_by`        varchar(64)     default ''               comment '更新者',
  `update_time`      datetime                                 comment '更新时间',
  primary key (user_id)
) engine=innodb auto_increment=1000 comment = '机构人员表';