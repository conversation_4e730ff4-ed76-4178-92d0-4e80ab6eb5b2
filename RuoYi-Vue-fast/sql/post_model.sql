-- ----------------------------
-- 岗位模型表
-- ----------------------------
DROP TABLE IF EXISTS `post_model`;
CREATE TABLE `post_model` (
  `post_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_name` varchar(50) NOT NULL COMMENT '岗位名称',
  `post_desc` varchar(500) DEFAULT NULL COMMENT '岗位描述',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='岗位模型表';

-- ----------------------------
-- 岗位模型与企业关联表
-- ----------------------------
DROP TABLE IF EXISTS `post_model_company`;
CREATE TABLE `post_model_company` (
  `post_id` bigint(20) NOT NULL COMMENT '岗位ID',
  `company_id` bigint(20) NOT NULL COMMENT '企业ID',
  PRIMARY KEY (`post_id`, `company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='岗位模型与企业关联表';

-- ----------------------------
-- 岗位模型数据请参考 test_data_init.sql 文件
-- ----------------------------
