drop table if exists `pro_training`;
create table `pro_training` (
  `project_id`       bigint(20)       not null auto_increment comment '项目id',
  `project_name`     varchar(20)      default ''              comment '项目名称',
  `training_mode`    varchar(30)      default ''            comment '培训模式',
  `training_days`    int(11)          default 0               comment '培训人日',
  `project_status`   varchar(20)      default ''              comment '项目状态',
  `start_date`       datetime                                 comment '培训开始日期',
  `end_date`         datetime                                 comment '培训截止日期',
  `project_desc`     varchar(600)                             comment '项目描述',
  `create_by`        varchar(64)      default ''              comment '创建者',
  `create_time`      datetime                                 comment '创建时间',
  `update_by`        varchar(64)      default ''              comment '更新者',
  `update_time`      datetime                                 comment '更新时间',
  primary key (project_id)
) engine=innodb auto_increment=1000 comment = '培训项目表';