-- ----------------------------
-- 人才培养方案表
-- ----------------------------
DROP TABLE IF EXISTS `training_program`;
CREATE TABLE `training_program` (
  `program_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '方案ID',
  `program_name` varchar(60) NOT NULL COMMENT '方案名称',
  `program_intro` varchar(600) DEFAULT NULL COMMENT '方案简介',
  `training_time` int(3) NOT NULL COMMENT '培训时间(年)',
  `trainee_count` int(5) NOT NULL COMMENT '预计培养人数(人)',
  `training_goal` varchar(600) DEFAULT NULL COMMENT '培养目标',
  `training_spec` varchar(600) DEFAULT NULL COMMENT '培养规格',
  `revision_count` int(3) DEFAULT 0 COMMENT '修订次数',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`program_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='人才培养方案表';

-- ----------------------------
-- 人才培养方案数据请参考 test_data_init.sql 文件
-- ----------------------------