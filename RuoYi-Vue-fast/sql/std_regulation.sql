drop table if exists `std_regulation`;
create table `std_regulation` (
  `regulation_id`      bigint(20)      not null auto_increment    comment '制度id',
  `regulation_name`    varchar(100)    default ''                 comment '制度名称',
  `attachment`         varchar(1000)   default ''                 comment '附件路径',
  `create_by`          varchar(64)     default ''                 comment '创建者',
  `create_time`        datetime                                   comment '创建时间',
  `update_by`          varchar(64)     default ''                 comment '更新者',
  `update_time`        datetime                                   comment '更新时间',
  primary key (regulation_id)
) engine=innodb auto_increment=100 comment = '制度表';