-- ----------------------------
-- 学生考试成绩表
-- ----------------------------
drop table if exists student_exam_score;
create table student_exam_score (
  score_id             bigint(20)      not null auto_increment    comment '成绩ID',
  student_id           bigint(20)      not null                   comment '学生ID',
  course_name          varchar(100)    not null                   comment '课程名称',
  course_type          char(1)         not null default '0'       comment '课程类型（0校内课程 1企业课程）',
  exam_type            varchar(50)     default ''                 comment '考试类型',
  score                decimal(5,2)    not null                   comment '成绩',
  create_by            varchar(64)     default ''                 comment '创建者',
  create_time          datetime                                   comment '创建时间',
  update_by            varchar(64)     default ''                 comment '更新者',
  update_time          datetime                                   comment '更新时间',
  remark               varchar(500)    default null               comment '备注',
  primary key (score_id),
  key idx_student_id (student_id)
) engine=innodb auto_increment=1 comment = '学生考试成绩表';

-- ----------------------------
-- 学生考试成绩数据请参考 test_data_init.sql 文件
-- ----------------------------