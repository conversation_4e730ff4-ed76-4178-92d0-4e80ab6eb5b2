drop table if exists `org_teacher`;
create table `org_teacher` (
  `teacher_id`       bigint(20)      not null auto_increment  comment '教师id',
  `teacher_name`     varchar(20)     default ''               comment '教师姓名',
  `teacher_type`     varchar(20)     default ''               comment '教师类型',
  `org_name`         varchar(30)     default ''               comment '所属组织名称',
  `company_name`     varchar(30)     default ''               comment '所属企业名称',
  `position`         varchar(30)     default ''               comment '职务',
  `gender`           char(1)         default ''               comment '性别（男/女）',
  `contact_info`     varchar(20)     default ''               comment '联系方式',
  `create_by`        varchar(64)     default ''               comment '创建者',
  `create_time`      datetime                                  comment '创建时间',
  `update_by`        varchar(64)     default ''               comment '更新者',
  `update_time`      datetime                                  comment '更新时间',
    primary key (teacher_id)
) engine=innodb auto_increment=10000 comment = '教师信息表';