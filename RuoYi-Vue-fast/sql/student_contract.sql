-- ----------------------------
-- 学生合同信息表
-- ----------------------------
drop table if exists student_contract;
create table student_contract (
  contract_id          bigint(20)      not null auto_increment    comment '合同ID',
  contract_number      varchar(50)     not null                   comment '合同编号',
  contract_name        varchar(100)    not null                   comment '合同名称',
  sign_date            date            not null                   comment '签约日期',
  student_id           bigint(20)      not null                   comment '学生ID',
  company_name         varchar(200)    not null                   comment '企业名称',
  post_name            varchar(200)    default ''                 comment '岗位名称',
  salary               decimal(10,2)   default null               comment '薪酬',
  create_by            varchar(64)     default ''                 comment '创建者',
  create_time          datetime                                   comment '创建时间',
  update_by            varchar(64)     default ''                 comment '更新者',
  update_time          datetime                                   comment '更新时间',
  remark               varchar(500)    default null               comment '备注',
  primary key (contract_id),
  unique key uk_contract_number (contract_number),
  key idx_student_id (student_id)
) engine=innodb auto_increment=1 comment = '学生合同信息表';

-- ----------------------------
-- 学生合同信息表数据请参考 test_data_init.sql 文件
-- ----------------------------