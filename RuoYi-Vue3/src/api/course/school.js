import request from '@/utils/request'

// 查询学校课程列表
export function listSchool(query) {
  return request({
    url: '/course/school/list',
    method: 'get',
    params: query
  })
}

// 查询学校课程详细
export function getSchool(courseId) {
  return request({
    url: '/course/school/' + courseId,
    method: 'get'
  })
}

// 新增学校课程
export function addSchool(data) {
  return request({
    url: '/course/school',
    method: 'post',
    data: data
  })
}

// 修改学校课程
export function updateSchool(data) {
  return request({
    url: '/course/school',
    method: 'put',
    data: data
  })
}

// 删除学校课程
export function delSchool(courseId) {
  return request({
    url: '/course/school/' + courseId,
    method: 'delete'
  })
}
