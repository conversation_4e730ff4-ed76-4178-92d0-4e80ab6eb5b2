import request from '@/utils/request'

// 查询企业课程列表
export function listCompany(query) {
  return request({
    url: '/course/company/list',
    method: 'get',
    params: query
  })
}

// 查询企业课程详细
export function getCompany(courseId) {
  return request({
    url: '/course/company/' + courseId,
    method: 'get'
  })
}

// 新增企业课程
export function addCompany(data) {
  return request({
    url: '/course/company',
    method: 'post',
    data: data
  })
}

// 修改企业课程
export function updateCompany(data) {
  return request({
    url: '/course/company',
    method: 'put',
    data: data
  })
}

// 删除企业课程
export function delCompany(courseId) {
  return request({
    url: '/course/company/' + courseId,
    method: 'delete'
  })
}
