import request from '@/utils/request'

/**
 * 获取岗位能力达标统计数据
 *
 * 后端需要实现的逻辑：
 * 1. 获取所有学生信息
 * 2. 按岗位分组学生（基于postName或targetPosition）
 * 3. 对每个岗位的每个学生：
 *    - 获取岗位的职业能力配置
 *    - 获取学生的课程成绩
 *    - 计算每个职业能力的分值（课程成绩按权重加权平均）
 *    - 统计未达标的职业能力数量（分值 < 职业能力阈值）
 *    - 判断学生是否达标（未达标数量 < 学生的positionCompetencyThreshold）
 * 4. 计算每个岗位的达标率（达标学生数 / 该岗位学生总数 * 100%）
 *
 * 期望返回数据格式：
 * {
 *   "code": 200,
 *   "data": [
 *     {
 *       "postName": "计算机科学与技术",
 *       "totalStudents": 50,
 *       "qualifiedStudents": 40,
 *       "competencyRate": 80
 *     },
 *     {
 *       "postName": "电气工程及其自动化",
 *       "totalStudents": 30,
 *       "qualifiedStudents": 24,
 *       "competencyRate": 80
 *     }
 *   ]
 * }
 */
export function getPositionCompetencyStats() {
  return request({
    url: '/profile/group/positionCompetencyStats',
    method: 'get'
  })
}
