import request from '@/utils/request'

// 查询评价记录列表
export function listEvaluationRecord(query) {
  return request({
    url: '/training/record/list',
    method: 'get',
    params: query
  })
}

// 根据学生ID查询评价记录列表
export function listEvaluationRecordByStudent(studentId) {
  return request({
    url: `/training/record/listByStudent/${studentId}`,
    method: 'get'
  })
}

// 查询评价记录详细
export function getEvaluationRecord(recordId) {
  return request({
    url: `/training/record/${recordId}`,
    method: 'get'
  })
}

// 获取评价记录的答题详情
export function getEvaluationRecordAnswers(recordId) {
  return request({
    url: `/training/record/answers/${recordId}`,
    method: 'get'
  })
}

// 新增评价记录
export function addEvaluationRecord(data) {
  return request({
    url: '/training/record',
    method: 'post',
    data: data
  })
}

// 创建评价记录并选择题目
export function createEvaluationRecord(data) {
  return request({
    url: '/training/record/create',
    method: 'post',
    data: data
  })
}

// 提交答题记录
export function submitAnswers(recordId, data) {
  return request({
    url: `/training/record/submit/${recordId}`,
    method: 'post',
    data: data
  })
}

// 修改评价记录
export function updateEvaluationRecord(data) {
  return request({
    url: '/training/record',
    method: 'put',
    data: data
  })
}

// 删除评价记录
export function delEvaluationRecord(recordIds) {
  return request({
    url: '/training/record/delete',
    method: 'post',
    data: recordIds
  })
}

// 查询题目列表
export function listEvaluationQuestion(query) {
  return request({
    url: '/training/question/list',
    method: 'get',
    params: query
  })
}
