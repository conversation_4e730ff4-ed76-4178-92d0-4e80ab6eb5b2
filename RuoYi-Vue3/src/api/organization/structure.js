import request from '@/utils/request'

// 查询机构信息列表
export function listStructure(query) {
  return request({
    url: '/organization/structure/list',
    method: 'get',
    params: query
  })
}

// 查询机构信息详细
export function getStructure(structureId) {
  return request({
    url: '/organization/structure/' + structureId,
    method: 'get'
  })
}

// 新增机构信息
export function addStructure(data) {
  return request({
    url: '/organization/structure',
    method: 'post',
    data: data
  })
}

// 修改机构信息
export function updateStructure(data) {
  return request({
    url: '/organization/structure',
    method: 'put',
    data: data
  })
}

// 删除机构信息
export function delStructure(structureId) {
  return request({
    url: '/organization/structure/' + structureId,
    method: 'delete'
  })
}
