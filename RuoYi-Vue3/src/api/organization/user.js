import request from '@/utils/request'

// 查询机构人员列表
export function listUser(query) {
  return request({
    url: '/organization/user/list',
    method: 'get',
    params: query
  })
}

// 查询机构人员详细
export function getUser(userId) {
  return request({
    url: '/organization/user/' + userId,
    method: 'get'
  })
}

// 新增机构人员
export function addUser(data) {
  return request({
    url: '/organization/user',
    method: 'post',
    data: data
  })
}

// 修改机构人员
export function updateUser(data) {
  return request({
    url: '/organization/user',
    method: 'put',
    data: data
  })
}

// 删除机构人员
export function delUser(userId) {
  return request({
    url: '/organization/user/' + userId,
    method: 'delete'
  })
}
