import request from '@/utils/request'

// 查询专业列表
export function listMajor(query) {
  return request({
    url: '/organization/major/list',
    method: 'get',
    params: query
  })
}

// 查询专业详细
export function getMajor(majorId) {
  return request({
    url: '/organization/major/' + majorId,
    method: 'get'
  })
}

// 新增专业
export function addMajor(data) {
  return request({
    url: '/organization/major',
    method: 'post',
    data: data
  })
}

// 修改专业
export function updateMajor(data) {
  return request({
    url: '/organization/major',
    method: 'put',
    data: data
  })
}

// 删除专业
export function delMajor(majorId) {
  return request({
    url: '/organization/major/' + majorId,
    method: 'delete'
  })
}
