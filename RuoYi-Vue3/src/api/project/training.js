import request from '@/utils/request'

// 查询培训项目列表
export function listTraining(query) {
  return request({
    url: '/project/training/list',
    method: 'get',
    params: query
  })
}

// 查询培训项目详细
export function getTraining(projectId) {
  return request({
    url: '/project/training/' + projectId,
    method: 'get'
  })
}

// 新增培训项目
export function addTraining(data) {
  return request({
    url: '/project/training',
    method: 'post',
    data: data
  })
}

// 修改培训项目
export function updateTraining(data) {
  return request({
    url: '/project/training',
    method: 'put',
    data: data
  })
}

// 删除培训项目
export function delTraining(projectId) {
  return request({
    url: '/project/training/' + projectId,
    method: 'delete'
  })
}
