import request from '@/utils/request'

// 查询培训模式列表
export function listMode(query) {
  return request({
    url: '/project/mode/list',
    method: 'get',
    params: query
  })
}

// 查询培训模式详细
export function getMode(modeId) {
  return request({
    url: '/project/mode/' + modeId,
    method: 'get'
  })
}

// 新增培训模式
export function addMode(data) {
  return request({
    url: '/project/mode',
    method: 'post',
    data: data
  })
}

// 修改培训模式
export function updateMode(data) {
  return request({
    url: '/project/mode',
    method: 'put',
    data: data
  })
}

// 删除培训模式
export function delMode(modeId) {
  return request({
    url: '/project/mode/' + modeId,
    method: 'delete'
  })
}
