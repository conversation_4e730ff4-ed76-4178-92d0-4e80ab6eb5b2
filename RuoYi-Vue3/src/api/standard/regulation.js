import request from '@/utils/request'

// 查询制度列表
export function listRegulation(query) {
  return request({
    url: '/standard/regulation/list',
    method: 'get',
    params: query
  })
}

// 查询制度详细
export function getRegulation(regulationId) {
  return request({
    url: '/standard/regulation/' + regulationId,
    method: 'get'
  })
}

// 新增制度
export function addRegulation(data) {
  return request({
    url: '/standard/regulation',
    method: 'post',
    data: data
  })
}

// 修改制度
export function updateRegulation(data) {
  return request({
    url: '/standard/regulation',
    method: 'put',
    data: data
  })
}

// 删除制度
export function delRegulation(regulationId) {
  return request({
    url: '/standard/regulation/' + regulationId,
    method: 'delete'
  })
}
