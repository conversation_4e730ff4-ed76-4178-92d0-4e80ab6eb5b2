<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="制度名称" prop="regulationName">
        <el-input
          v-model="queryParams.regulationName"
          placeholder="请输入制度名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['standard:regulation:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['standard:regulation:remove']"
          >批量删除</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      border
      :data="regulationList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="80" align="center" />
      <el-table-column label="制度名称" align="center" prop="regulationName" />
      <el-table-column label="附件路径" align="center" prop="attachment" />
      <el-table-column
        label="操作"
        width="180"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['standard:regulation:edit']"
            >编辑</el-button
          >
          <el-button
            link
            type="primary"
            @click="handleDownload(scope.row)"
            v-hasPermi="['standard:regulation:download']"
            >下载</el-button
          >
          <el-button
            link
            type="primary"
            @click="handleDelete(scope.row)"
            v-hasPermi="['standard:regulation:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改制度对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form
        ref="regulationRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item
          label="制度名称"
          prop="regulationName"
          label-position="top"
        >
          <el-input
            v-model="form.regulationName"
            placeholder="请输入制度名称"
            :maxlength="40"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="附件路径" prop="attachment" label-position="top">
          <file-upload v-model="form.attachment" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Regulation">
import {
  listRegulation,
  getRegulation,
  delRegulation,
  addRegulation,
  updateRegulation,
} from "@/api/standard/regulation";

const { proxy } = getCurrentInstance();

const regulationList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    regulationName: null,
  },
  rules: {
    regulationName: [
      { required: true, message: "制度名称不能为空", trigger: ["blur"] },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询制度列表 */
function getList() {
  loading.value = true;
  listRegulation(queryParams.value).then((response) => {
    regulationList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    regulationId: null,
    regulationName: null,
    attachment: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
  };
  proxy.resetForm("regulationRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.regulationId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加制度";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _regulationId = row.regulationId || ids.value;
  getRegulation(_regulationId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改制度";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["regulationRef"].validate((valid) => {
    if (valid) {
      if (form.value.regulationId != null) {
        updateRegulation(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addRegulation(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _regulationIds = row.regulationId || ids.value;
  const tipMsg =
    ids.value.length == 0
      ? `是否确认删除"${row.regulationName}"?`
      : "是否确认删除所选数据？";
  proxy.$modal
    .confirm(tipMsg)
    .then(function () {
      return delRegulation(_regulationIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "standard/regulation/export",
    {
      ...queryParams.value,
    },
    `regulation_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
