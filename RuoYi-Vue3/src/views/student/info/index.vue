<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="88px">
      <el-form-item label="学生学号" prop="studentNumber">
        <el-input v-model="queryParams.studentNumber" placeholder="请输入学生学号" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="学生姓名" prop="studentName">
        <el-input v-model="queryParams.studentName" placeholder="请输入学生姓名" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="班级" prop="className">
        <el-input v-model="queryParams.className" placeholder="请输入班级" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="学生状态" prop="studentStatus">
        <el-select v-model="queryParams.studentStatus" style="width: 200px;" placeholder="请选择学生状态" clearable>
          <el-option label="未面试" value="0" />
          <el-option label="面试失败" value="1" />
          <el-option label="成功签约" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">批量删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="openImportDialog">导入学生信息</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-dropdown @command="handleImportScore">
          <el-button type="warning" plain icon="Upload">
            导入学生成绩<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="school">导入校内学科成绩</el-dropdown-item>
              <el-dropdown-item command="company">导入企业课程成绩</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="studentInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="studentId" width="80" />
      <el-table-column label="学生姓名" align="center" prop="studentName" />
      <el-table-column label="学号" align="center" prop="studentNumber" />
      <el-table-column label="班级" align="center" prop="className" />
      <el-table-column label="性别" align="center" prop="gender">
        <template #default="scope">
          <span v-if="scope.row.gender === '0'">男</span>
          <span v-else>女</span>
        </template>
      </el-table-column>
      <el-table-column label="学生状态" align="center" prop="studentStatus">
        <template #default="scope">
          <el-tag v-if="scope.row.studentStatus === '0'" type="info">未面试</el-tag>
          <el-tag v-else-if="scope.row.studentStatus === '1'" type="danger">面试失败</el-tag>
          <el-tag v-else-if="scope.row.studentStatus === '2'" type="success">成功签约</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="校内教师" align="center" prop="schoolTeacher" />
      <el-table-column label="企业导师" align="center" prop="companyTeacher" />
      <el-table-column label="目标岗位" align="center" prop="targetPosition">
        <template #default="scope">
          <span v-if="scope.row.postName">{{ scope.row.postName }}</span>
          <span v-else-if="scope.row.targetPosition">{{ scope.row.targetPosition }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="入驻企业" align="center" prop="residentCompany">
        <template #default="scope">
          <span v-if="scope.row.contractCompanyName">{{ scope.row.contractCompanyName }}</span>
          <span v-else-if="scope.row.residentCompany">{{ scope.row.residentCompany }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="薪酬" align="center" prop="salary">
        <template #default="scope">
          <span v-if="scope.row.contractSalary">{{ scope.row.contractSalary }}元</span>
          <span v-else-if="scope.row.salary">{{ scope.row.salary }}元</span>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary"  @click="handleView(scope.row)">查看</el-button>
          <el-button link type="primary"  @click="handleUpdate(scope.row)">修改</el-button>
          <el-button link type="primary" @click="handleGrowthPath(scope.row)">成长路径</el-button>
          <el-button link type="primary" @click="handleExamScore(scope.row)">考试成绩</el-button>
          <el-button 
            v-if="scope.row.studentStatus === '0'" 
            link 
            type="primary" 
            @click="handleInterview(scope.row)"
          >
            面试
          </el-button>
          <el-button 
            v-if="scope.row.studentStatus === '1'" 
            link 
            type="primary" 
            @click="handleModifyStatus(scope.row)"
          >
            修改状态
          </el-button>
          <el-button link type="primary"  @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page="queryParams.pageNum"
      :limit="queryParams.pageSize"
      @pagination="handlePagination"
    />

    <!-- 学生信息统一对话框 -->
    <el-dialog 
      v-model="studentDialogVisible" 
      :title="studentDialogTitle" 
      width="800px" 
      append-to-body
      :close-on-click-modal="false"
    >
      <StudentBasicInfo
        ref="studentBasicInfoRef"
        :mode="studentDialogMode"
        :student-info="currentStudentInfo"
        :major-options="majorOptions"
        :school-teacher-options="schoolTeacherOptions"
        :company-teacher-options="companyTeacherOptions"
        @success="handleStudentSuccess"
        @selectContract="handleStudentSelectContract"
        @createContract="handleStudentCreateContract"
      />
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleStudentCancel">取消</el-button>
          <el-button 
            v-if="studentDialogMode !== 'view'" 
            type="primary" 
            @click="handleStudentSubmit" 
            :loading="studentSubmitLoading"
          >
            {{ studentDialogMode === 'add' ? '新增' : '修改' }}
          </el-button>
          <el-button 
            v-if="studentDialogMode === 'view'" 
            type="primary" 
            @click="handleStudentCancel"
          >
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 成长路径弹框 -->
    <GrowthPathDialog
      v-model="growthPathDialogVisible"
      :student-info="currentStudent"
      @success="handleGrowthPathSuccess"
    />

    <!-- 考试成绩弹框 -->
    <ExamScoreDialog
      v-model="examScoreDialogVisible"
      :student-info="currentExamStudent"
      @success="handleExamScoreSuccess"
    />

    <!-- 状态管理对话框 -->
    <StatusDialog
      v-model="statusDialogVisible"
      :mode="statusDialogMode"
      :student-info="currentStatusStudent"
      @success="handleStatusSuccess"
    />

    <!-- 选择合同对话框 -->
    <el-dialog 
      v-model="contractSelectDialogVisible" 
      title="选择合同" 
      width="800px" 
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form :model="contractQueryParams" :inline="true" style="margin-bottom: 16px;">
        <el-form-item label="合同编号">
          <el-input v-model="contractQueryParams.contractNumber" placeholder="请输入合同编号" clearable />
        </el-form-item>
        <el-form-item label="合同名称">
          <el-input v-model="contractQueryParams.contractName" placeholder="请输入合同名称" clearable />
        </el-form-item>
        <el-form-item label="企业名称">
          <el-input v-model="contractQueryParams.companyName" placeholder="请输入企业名称" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleContractQuery">搜索</el-button>
          <el-button @click="handleContractReset">重置</el-button>
        </el-form-item>
      </el-form>
      
      <el-table 
        v-loading="contractLoading" 
        :data="contractList" 
        @row-click="handleContractSelect"
        style="cursor: pointer;"
      >
        <el-table-column label="合同编号" prop="contractNumber" />
        <el-table-column label="合同名称" prop="contractName" />
        <el-table-column label="企业名称" prop="companyName" />
        <el-table-column label="签约日期" prop="signDate" />
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button type="primary" link @click="handleContractSelect(scope.row)">选择</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="contractTotal > 0"
        :total="contractTotal"
        :page="contractQueryParams.pageNum"
        :limit="contractQueryParams.pageSize"
        @pagination="getContractList"
      />
    </el-dialog>

    <!-- 导入学生信息对话框 -->
    <ImportDialog
      v-model="importDialogVisible"
      :title="importTitle"
      :import-url="importUrl"
      :template-url="templateUrl"
      @success="handleImportSuccess"
      @error="handleImportError"
      @download-template="handleDownloadTemplate"
    />

    <!-- 导入学生成绩对话框 -->
    <ImportDialog
      v-model="scoreImportDialogVisible"
      :title="scoreImportTitle"
      :import-url="getScoreImportUrl()"
      :template-url="getScoreTemplateUrl()"
      @success="handleScoreImportSuccess"
      @error="handleScoreImportError"
      @download-template="handleDownloadScoreTemplate"
    />
  </div>
</template>

<script setup name="StudentInfo">
import { listStudentInfo, getStudentInfo, delStudentInfo, addStudentInfo, updateStudentInfo } from "@/api/student/student";
import { listPost } from "@/api/training/post";
import { listContract } from "@/api/student/contract";
import { listTeacher } from "@/api/organization/teacher";
import { listMajor } from "@/api/organization/major";
import ExamScoreDialog from "./components/ExamScoreDialog.vue";
import GrowthPathDialog from "./components/GrowthPathDialog.vue";
import StudentBasicInfo from "./components/StudentBasicInfo.vue";
import StatusDialog from "./components/StatusDialog.vue";
import ImportDialog from "@/components/ImportDialog/index.vue";
import { useImport, createImportConfig } from "@/composables/useImport";

const { proxy } = getCurrentInstance();
const { sys_user_sex } = proxy.useDict('sys_user_sex');

const studentInfoList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

// 学生信息对话框相关数据
const studentDialogVisible = ref(false);
const studentDialogMode = ref('view'); // 'view' | 'edit' | 'add'
const currentStudentInfo = ref({});
const studentBasicInfoRef = ref();
const studentSubmitLoading = ref(false);

// 合同选择相关数据
const contractSelectDialogVisible = ref(false);
const contractLoading = ref(false);
const contractList = ref([]);
const contractTotal = ref(0);
const contractQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  contractNumber: '',
  contractName: '',
  companyName: ''
});
const contractSelectCallback = ref(null);

// 学生对话框标题
const studentDialogTitle = computed(() => {
  const titles = {
    view: '查看学生信息',
    edit: '修改学生信息',
    add: '新增学生信息'
  }
  return titles[studentDialogMode.value];
});

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    studentName: null,
    studentNumber: null,
    className: null,
    studentStatus: null
  }
});

const { queryParams } = toRefs(data);

// 校内教师选项
const schoolTeacherOptions = ref([]);

// 企业导师选项
const companyTeacherOptions = ref([]);

// 专业选项
const majorOptions = ref([]);

// 岗位选项数据
const postOptions = ref([]);

// 成长路径相关数据
const growthPathDialogVisible = ref(false);
const currentStudent = ref({});

// 考试成绩相关数据
const examScoreDialogVisible = ref(false);
const currentExamStudent = ref({});

// 状态管理对话框相关数据
const statusDialogVisible = ref(false);
const statusDialogMode = ref('interview'); // 'interview' | 'modify'
const currentStatusStudent = ref({});

// 使用导入组合式函数
const importConfig = createImportConfig(
  '/student/info',
  '学生信息',
  getList,
  proxy.download
)

const {
  importDialogVisible,
  importTitle,
  openImportDialog,
  handleImportSuccess,
  handleImportError,
  handleDownloadTemplate,
  importUrl,
  templateUrl
} = useImport(importConfig)

// 成绩导入相关数据
const scoreImportDialogVisible = ref(false);
const scoreImportTitle = ref('');
const scoreImportType = ref(''); // 'school' 或 'company'

/** 查询学生信息列表 */
function getList() {
  loading.value = true;
  listStudentInfo(queryParams.value).then(response => {
    studentInfoList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 分页处理 */
function handlePagination(pagination) {
  queryParams.value.pageNum = pagination.page;
  queryParams.value.pageSize = pagination.limit;
  getList();
}

// 取消按钮


/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.studentId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  console.log('Add button clicked')
  studentDialogMode.value = 'add';
  currentStudentInfo.value = {};
  studentDialogVisible.value = true;
  console.log('Dialog state:', { 
    mode: studentDialogMode.value, 
    studentInfo: currentStudentInfo.value, 
    visible: studentDialogVisible.value 
  })
}

/** 修改按钮操作 */
function handleUpdate(row) {
  const studentId = row.studentId || row.student_id;
  getStudentInfo(studentId).then(response => {
    studentDialogMode.value = 'edit';
    currentStudentInfo.value = response.data;
    studentDialogVisible.value = true;
  });
}

/** 查看按钮操作 */
function handleView(row) {
  const studentId = row.studentId || row.student_id;
  getStudentInfo(studentId).then(response => {
    studentDialogMode.value = 'view';
    currentStudentInfo.value = response.data;
    studentDialogVisible.value = true;
        });
}

/** 学生信息操作成功回调 */
function handleStudentSuccess(response) {
  const message = studentDialogMode.value === 'add' ? '新增成功' : '修改成功';
  proxy.$modal.msgSuccess(message);
  studentDialogVisible.value = false;
          getList();
}

/** 学生信息提交 */
function handleStudentSubmit() {
  studentSubmitLoading.value = true;
  studentBasicInfoRef.value.submit().then(() => {
    studentSubmitLoading.value = false;
  }).catch(() => {
    studentSubmitLoading.value = false;
  });
}

/** 学生信息取消 */
function handleStudentCancel() {
  studentDialogVisible.value = false;
}

/** 学生信息选择合同 */
function handleStudentSelectContract(callback) {
  contractSelectCallback.value = callback;
  contractSelectDialogVisible.value = true;
  getContractList();
}

/** 获取合同列表 */
function getContractList() {
  contractLoading.value = true;
  listContract(contractQueryParams.value).then(response => {
    contractList.value = response.rows;
    contractTotal.value = response.total;
    contractLoading.value = false;
  }).catch(() => {
    contractLoading.value = false;
  });
}

/** 合同查询操作 */
function handleContractQuery() {
  contractQueryParams.value.pageNum = 1;
  getContractList();
}

/** 合同查询重置 */
function handleContractReset() {
  contractQueryParams.value = {
    pageNum: 1,
    pageSize: 10,
    contractNumber: '',
    contractName: '',
    companyName: ''
  };
  getContractList();
}

/** 选择合同 */
function handleContractSelect(row) {
  if (contractSelectCallback.value) {
    contractSelectCallback.value(row);
  }
  contractSelectDialogVisible.value = false;
  contractSelectCallback.value = null;
}

/** 学生信息创建合同 */
function handleStudentCreateContract() {
  proxy.$tab.openPage("新增合同", "/contract-info/add");
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _studentIds = row.studentId || ids.value;
  proxy.$modal.confirm('是否确认删除学生信息编号为"' + _studentIds + '"的数据项？').then(function() {
    loading.value = true;
    return delStudentInfo(_studentIds);
  }).then(() => {
    proxy.$modal.msgSuccess("删除成功");
    getList();
  }).catch(() => {
    loading.value = false;
  });
}

/** 获取所有岗位选项 */
function getAllPosts() {
  listPost({ pageNum: 1, pageSize: 1000 }).then(response => {
    postOptions.value = response.rows || [];
  }).catch(() => {
    console.error('获取岗位列表失败');
  });
}

/** 获取校内教师选项 */
function getSchoolTeachers() {
  listTeacher({
    pageNum: 1,
    pageSize: 10000,
    teacherType: '校内教师' // 根据教师类型筛选校内教师
  }).then(response => {
    schoolTeacherOptions.value = (response.rows || []).map(teacher => ({
      label: teacher.teacherName,
      value: teacher.teacherName
    }));
  }).catch(() => {
    console.error('获取校内教师列表失败');
  });
}

/** 获取企业导师选项 */
function getCompanyTeachers() {
  listTeacher({
    pageNum: 1,
    pageSize: 10000,
    teacherType: '企业导师' // 根据教师类型筛选企业导师
  }).then(response => {
    companyTeacherOptions.value = (response.rows || []).map(teacher => ({
      label: teacher.teacherName,
      value: teacher.teacherName
    }));
  }).catch(() => {
    console.error('获取企业导师列表失败');
  });
}

/** 获取专业选项 */
function getMajors() {
  listMajor({ pageNum: 1, pageSize: 1000 }).then(response => {
    majorOptions.value = (response.rows || []).map(major => ({
      label: major.majorName,
      value: major.majorName
    }));
  }).catch(() => {
    console.error('获取专业列表失败');
  });
}

/** 成长路径按钮操作 */
function handleGrowthPath(row) {
  currentStudent.value = { ...row };
  growthPathDialogVisible.value = true;
}

/** 成长路径成功回调 */
function handleGrowthPathSuccess() {
  proxy.$modal.msgSuccess('操作成功');
}

/** 打开考试成绩弹框 */
function handleExamScore(row) {
  currentExamStudent.value = { ...row };
  examScoreDialogVisible.value = true;
}

/** 考试成绩成功回调 */
function handleExamScoreSuccess() {
  proxy.$modal.msgSuccess('操作成功');
}



/** 面试按钮操作 */
function handleInterview(row) {
  statusDialogMode.value = 'interview';
  currentStatusStudent.value = { ...row };
  statusDialogVisible.value = true;
}

/** 修改状态按钮操作 */
function handleModifyStatus(row) {
  statusDialogMode.value = 'modify';
  currentStatusStudent.value = { ...row };
  statusDialogVisible.value = true;
}

/** 状态管理成功回调 */
function handleStatusSuccess(updateData) {
  updateStudentInfo(updateData).then(response => {
    const message = statusDialogMode.value === 'interview' ? '面试结果已保存' : '状态修改成功';
    proxy.$modal.msgSuccess(message);
    statusDialogVisible.value = false;
    getList(); // 刷新列表
  }).catch(() => {
    // 错误处理
  });
}

/** 处理成绩导入下拉菜单点击 */
function handleImportScore(command) {
  scoreImportType.value = command;
  if (command === 'school') {
    scoreImportTitle.value = '导入校内学科成绩';
  } else if (command === 'company') {
    scoreImportTitle.value = '导入企业课程成绩';
  }
  scoreImportDialogVisible.value = true;
}

/** 成绩导入成功回调 */
function handleScoreImportSuccess(message) {
  proxy.$modal.msgSuccess(message);
  scoreImportDialogVisible.value = false;
  // 可以选择是否刷新列表，成绩导入不影响学生信息列表
}

/** 成绩导入错误回调 */
function handleScoreImportError(error) {
  proxy.$modal.msgError(error);
}

/** 获取成绩导入URL */
function getScoreImportUrl() {
  return scoreImportType.value === 'school'
    ? '/student/score/importSchoolData'
    : '/student/score/importCompanyData';
}

/** 获取成绩模板URL */
function getScoreTemplateUrl() {
  return scoreImportType.value === 'school'
    ? '/student/score/importSchoolTemplate'
    : '/student/score/importCompanyTemplate';
}

/** 下载成绩导入模板 */
function handleDownloadScoreTemplate() {
  const templateUrl = getScoreTemplateUrl();
  const fileName = scoreImportType.value === 'school'
    ? '校内课程成绩导入模板'
    : '企业课程成绩导入模板';
  proxy.download(templateUrl, {}, fileName + '.xlsx');
}

onMounted(() => {
  getList();
  getAllPosts();
  getSchoolTeachers();
  getCompanyTeachers();
  getMajors();
});
</script>

 