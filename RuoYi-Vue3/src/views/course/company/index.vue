<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="课程名称" prop="courseName">
        <el-input
          v-model="queryParams.courseName"
          placeholder="请输入课程名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['course:company:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['course:company:remove']"
          >批量删除</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      border
      :data="companyList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="80" align="center" />
      <el-table-column label="课程名称" align="center" prop="courseName" />
      <el-table-column label="课程标准" align="center" prop="courseStandard" />
      <el-table-column label="课程学时" align="center" prop="courseHours" />
      <el-table-column label="课程学分" align="center" prop="courseCredit" />
      <el-table-column label="课程目标" align="center" prop="courseGoal" />
      <el-table-column
        width="180"
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
           <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleDetail(scope.row)"
            v-hasPermi="['course:company:edit']"
            >查看</el-button
          >
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['course:company:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['course:company:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改企业课程对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="companyRef" :model="form" :rules="rules" label-width="80px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="课程名称"
              prop="courseName"
              label-position="top"
            >
              <el-input
                v-model="form.courseName"
                placeholder="请输入课程名称"
                show-word-limit
                :maxlength="20"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="课程标准"
              prop="courseStandard"
              label-position="top"
            >
            <el-select
                v-model="form.courseStandard"
                placeholder="请选择课程标准"
              >
                <el-option
                  v-for="dict in courseOption"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="课程学时"
              prop="courseHours"
              label-position="top"
            >
              <el-input
                v-model="form.courseHours"
                placeholder="请输入课程学时"
                type="number"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="课程学分"
              prop="courseCredit"
              label-position="top"
            >
              <el-input
                v-model="form.courseCredit"
                placeholder="请输入课程学分"
                type="number"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="课程目标" prop="courseGoal" label-position="top">
          <el-input
            v-model="form.courseGoal"
            type="textarea"
            placeholder="请输入内容"
            show-word-limit
            :maxlength="600"
            :rows="5"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 详情 -->
    <el-dialog :title="title" v-model="detail" width="600px" append-to-body>
      <el-descriptions class="margin-top" :column="2" direction="vertical">
        <el-descriptions-item label="课程名称">{{
          form.courseName
        }}</el-descriptions-item>
        <el-descriptions-item label="课程标准">{{
          form.courseStandard || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="课程学时">{{
          form.courseHours || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="课程学分">{{
          form.courseCredit || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="课程目标">{{
          form.courseGoal || "-"
        }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detail = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Company">
import {
  listCompany,
  getCompany,
  delCompany,
  addCompany,
  updateCompany,
} from "@/api/course/company";

const { proxy } = getCurrentInstance();

const companyList = ref([]);
const open = ref(false);
const detail = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const courseOption = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    courseName: null,
  },
  rules: {
    courseName: [
      { required: true, message: "请输入课程名称", trigger: "blur" },
    ],
    courseStandard: [
      { required: true, message: "请选择课程标准", trigger: "change" },
    ],
    courseHours: [
      { required: true, message: "请输入课程学时", trigger: "blur" },
    ],
    courseCredit: [
      { required: true, message: "请输入课程学分", trigger: "blur" },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询企业课程列表 */
function getList() {
  loading.value = true;
  listCompany(queryParams.value).then((response) => {
    companyList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    courseId: null,
    courseName: null,
    courseStandard: null,
    courseHours: null,
    courseCredit: null,
    courseGoal: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
  };
  proxy.resetForm("companyRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.courseId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  getCourseList();
  open.value = true;
  title.value = "添加企业课程";
}

/** 修改按钮操作 */
function handleDetail(row) {
  reset();
  const _courseId = row.courseId || ids.value;
  getCompany(_courseId).then((response) => {
    form.value = response.data;
    detail.value = true;
    title.value = "详细";
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  getCourseList();
  const _courseId = row.courseId || ids.value;
  getSchool(_courseId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改学校课程";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["companyRef"].validate((valid) => {
    if (valid) {
      if (form.value.courseId != null) {
        updateCompany(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addCompany(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _courseIds = row.courseId || ids.value;
   const tipMsg =
    ids.value.length == 0
      ? `是否确认删除"${row.companyName}"?`
      : "是否确认删除所选数据？";
  proxy.$modal
    .confirm(tipMsg)
    .then(function () {
      return delCompany(_courseIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "course/company/export",
    {
      ...queryParams.value,
    },
    `company_${new Date().getTime()}.xlsx`
  );
}

/** 查询课程信息下拉 */
function getCourseList() {
  listCourse().then((response) => {
    if (response.rows.length > 0) {
      response.rows.forEach((item, index) => {
        courseOption.value.push({
          label: item.courseName,
          value: item.courseName,
        });
      });
    }
  });
}
getList();
</script>
