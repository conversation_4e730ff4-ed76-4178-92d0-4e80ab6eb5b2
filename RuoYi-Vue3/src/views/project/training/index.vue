<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="培训模式" prop="trainingMode">
        <el-select
          v-model="queryParams.trainingMode"
          placeholder="请选择培训模式"
          @keyup.enter="handleQuery"
          style="width: 240px"
          clearable
        >
          <el-option
            v-for="dict in modeOption"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['project:training:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['project:training:remove']"
          >批量删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['project:training:import']"
          >导入</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      border
      :data="trainingList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="80" align="center" />
      <el-table-column label="项目名称" align="left" prop="projectName" />
      <el-table-column
        label="培训模式"
        width="240"
        align="center"
        prop="trainingMode"
      />
      <el-table-column
        label="培训起止日期"
        align="center"
        prop="startDate"
        width="180"
      >
        <template #default="scope">
          <span>{{
            parseTime(scope.row.startDate, "{y}.{m}.{d}") +
            `-` +
            parseTime(scope.row.endDate, "{y}.{m}.{d}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="培训人日"
        width="80"
        align="center"
        prop="trainingDays"
      />
      <el-table-column
        label="项目状态"
        width="80"
        align="center"
        prop="projectStatus"
      >
        <template #default="scope">
          <dict-tag
            :options="project_status"
            :value="scope.row.projectStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        width="240"
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleDetail(scope.row)"
            v-hasPermi="['project:training:detail']"
            >查看</el-button
          >
          <el-button
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['project:training:edit']"
            >编辑</el-button
          >
          <el-button
            link
            type="primary"
            @click="handleStatus(scope.row)"
            v-hasPermi="['project:training:status']"
            >变更状态</el-button
          >
          <el-button
            link
            type="primary"
            @click="handleDelete(scope.row)"
            v-hasPermi="['project:training:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改培训项目对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form
        ref="trainingRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="项目名称"
              prop="projectName"
              label-position="top"
            >
              <el-input
                v-model="form.projectName"
                placeholder="请输入项目名称"
                show-word-limit
                :maxlength="20"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="培训模式"
              prop="trainingMode"
              label-position="top"
            >
              <el-select
                v-model="form.trainingMode"
                placeholder="请选择培训模式"
              >
                <el-option
                  v-for="dict in modeOption"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="培训人日"
              prop="trainingDays"
              label-position="top"
            >
              <el-input
                v-model="form.trainingDays"
                type="number"
                min="0"
                placeholder="请输入培训人日"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="项目状态"
              prop="projectStatus"
              label-position="top"
            >
              <el-select
                v-model="form.projectStatus"
                placeholder="请选择项目状态"
              >
                <el-option
                  v-for="dict in project_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="培训开始日期"
              prop="dateRange"
              label-position="top"
            >
              <el-date-picker
                v-model="form.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="请选择培训开始日期"
                @change="handleDateChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="项目描述" prop="projectDesc" label-position="top">
          <el-input
            v-model="form.projectDesc"
            type="textarea"
            placeholder="请输入内容"
            show-word-limit
            :maxlength="600"
            :rows="5"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 添加或修改培训项目对话框 -->
    <el-dialog :title="title" v-model="detail" width="600px" append-to-body>
      <el-descriptions class="margin-top" :column="2" direction="vertical">
        <el-descriptions-item label="培训项目名称">{{
          form.projectName
        }}</el-descriptions-item>
        <el-descriptions-item label="培训模式">{{
          form.trainingMode || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="培训人日">{{
          form.trainingDays || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="项目状态">{{
          form.projectStatus || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="培训起止日期" span="2">{{
          parseTime(form.dateRange[0], "{y}.{m}.{d}") +
            `-` +
            parseTime(form.dateRange[1], "{y}.{m}.{d}") || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="项目描述">{{
          form.projectDesc || "-"
        }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detail = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 添加或修改培训项目对话框 -->
    <el-dialog :title="title" v-model="status" width="500px" append-to-body>
      <el-form
        ref="trainingRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item
          label="项目状态"
          prop="projectStatus"
          label-position="top"
        >
          <el-select v-model="form.projectStatus" placeholder="请选择项目状态">
            <el-option
              v-for="dict in project_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="status = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 导入对话框 -->
    <ImportDialog
      v-model="importDialogVisible"
      :title="importTitle"
      :import-url="importUrl"
      :template-url="templateUrl"
      @success="handleImportSuccess"
      @error="handleImportError"
      @download-template="handleDownloadTemplate"
    />
  </div>
</template>

<script setup name="Training">
import {
  listTraining,
  getTraining,
  delTraining,
  addTraining,
  updateTraining,
} from "@/api/project/training";
import { listMode } from "@/api/project/mode";
import ImportDialog from "@/components/ImportDialog/index.vue";
import { useImport, createImportConfig } from "@/composables/useImport";

const { proxy } = getCurrentInstance();
const { project_status } = proxy.useDict("project_status");

const trainingList = ref([]);
const open = ref(false);
const detail = ref(false);
const status = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const modeOption = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectName: null,
    trainingMode: null,
  },
  rules: {
    projectName: [
      { required: true, message: "请输入项目名称！", trigger: "blur" },
    ],
    projectStatus: [
      { required: true, message: "请选择项目状态！", trigger: "change" },
    ],
    dateRange: [
      { required: true, message: "请选择培训起止日期！", trigger: "change" },
    ],
  },
});
import { parseTime } from "@/utils/ruoyi";
const { queryParams, form, rules } = toRefs(data);

// 使用导入组合式函数
const importConfig = createImportConfig(
  "/project/training",
  "培训项目",
  getList,
  proxy.download
);

const {
  importDialogVisible,
  importTitle,
  openImportDialog,
  handleImportSuccess,
  handleImportError,
  handleDownloadTemplate,
  importUrl,
  templateUrl,
} = useImport(importConfig);

/** 查询培训项目列表 */
function getList() {
  loading.value = true;
  listTraining(queryParams.value).then((response) => {
    trainingList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    projectId: null,
    projectName: null,
    trainingMode: null,
    trainingDays: null,
    projectStatus: null,
    startDate: null,
    endDate: null,
    projectDesc: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    dateRange: [],
  };
  proxy.resetForm("trainingRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.projectId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加培训项目";
}

/** 导入按钮操作 */
function handleImport() {
  openImportDialog();
}

/** 详细按钮操作 */
function handleDetail(row) {
  reset();
  const _projectId = row.projectId || ids.value;
  getTraining(_projectId).then((response) => {
    form.value = response.data;
    form.value.dateRange = [
      parseTime(response.data.startDate, "{y}-{m}-{d}"),
      parseTime(response.data.endDate, "{y}-{m}-{d}"),
    ];
    detail.value = true;
    title.value = "详细";
  });
}
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _projectId = row.projectId || ids.value;
  getTraining(_projectId).then((response) => {
    form.value = response.data;
    form.value.dateRange = [
      parseTime(response.data.startDate, "{y}-{m}-{d}"),
      parseTime(response.data.endDate, "{y}-{m}-{d}"),
    ];
    open.value = true;
    title.value = "修改培训项目";
  });
}
/** 状态按钮操作 */
function handleStatus(row) {
  reset();
  const _projectId = row.projectId || ids.value;
  getTraining(_projectId).then((response) => {
    form.value = response.data;
    form.value.dateRange = [
      parseTime(response.data.startDate, "{y}-{m}-{d}"),
      parseTime(response.data.endDate, "{y}-{m}-{d}"),
    ];
    status.value = true;
    title.value = "变更状态";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["trainingRef"].validate((valid) => {
    if (valid) {
      if (form.value.projectId != null) {
        updateTraining(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          status.value = open.value = false;
          getList();
        });
      } else {
        addTraining(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          status.value = open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _projectIds = row.projectId || ids.value;
  const tipMsg =
    ids.value.length == 0
      ? `是否确认删除"${row.projectName}"?`
      : "是否确认删除所选数据？";
  proxy.$modal
    .confirm(tipMsg)
    .then(function () {
      return delTraining(_projectIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "project/training/export",
    {
      ...queryParams.value,
    },
    `training_${new Date().getTime()}.xlsx`
  );
}
// 处理日期范围变化
const handleDateChange = (value) => {
  if (value && value.length === 2) {
    [form.value.startDate, form.value.endDate] = value;
  } else {
    form.value.startDate = "";
    form.value.endDate = "";
  }
};

/** 查询模式信息下拉 */
function getModeList() {
  listMode().then((response) => {
    if (response.rows.length > 0) {
      response.rows.forEach((item, index) => {
        modeOption.value.push({
          label: item.modeName,
          value: item.modeName,
        });
      });
    }
  });
}
getList();

getModeList();
</script>
