<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="模式名称" prop="modeName">
        <el-input
          v-model="queryParams.modeName"
          placeholder="请输入模式名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['project:mode:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['project:mode:remove']"
        >批量删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['project:mode:import']"
          >导入</el-button
        >
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border :data="modeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="80" align="center" />
      <el-table-column label="模式名称" align="elft" prop="modeName" />
      <el-table-column label="操作" width="180" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" @click="handleUpdate(scope.row)" v-hasPermi="['project:mode:edit']">编辑</el-button>
          <el-button link type="primary" @click="handleDelete(scope.row)" v-hasPermi="['project:mode:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改培训模式对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="modeRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="模式名称" prop="modeName" label-position="top">
          <el-input v-model="form.modeName" placeholder="请输入模式名称" show-word-limit :maxlength="20" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 导入对话框 -->
    <ImportDialog
      v-model="importDialogVisible"
      :title="importTitle"
      :import-url="importUrl"
      :template-url="templateUrl"
      @success="handleImportSuccess"
      @error="handleImportError"
      @download-template="handleDownloadTemplate"
    />
  </div>
</template>

<script setup name="Mode">
import { listMode, getMode, delMode, addMode, updateMode } from "@/api/project/mode"
import ImportDialog from "@/components/ImportDialog/index.vue";
import { useImport, createImportConfig } from "@/composables/useImport";

const { proxy } = getCurrentInstance()

const modeList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    modeName: null,
  },
  rules: {
    modeName: [
      { required: true, message: "请输入模式名称！", trigger: "blur" },
    ],
  }
})

const { queryParams, form, rules } = toRefs(data)
// 使用导入组合式函数
const importConfig = createImportConfig(
  "/project/mode",
  "培训模式",
  getList,
  proxy.download
);

const {
  importDialogVisible,
  importTitle,
  openImportDialog,
  handleImportSuccess,
  handleImportError,
  handleDownloadTemplate,
  importUrl,
  templateUrl,
} = useImport(importConfig);

/** 查询培训模式列表 */
function getList() {
  loading.value = true
  listMode(queryParams.value).then(response => {
    modeList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    modeId: null,
    modeName: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null
  }
  proxy.resetForm("modeRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.modeId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加培训模式"
}

/** 导入按钮操作 */
function handleImport() {
  openImportDialog();
}
/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _modeId = row.modeId || ids.value
  getMode(_modeId).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改培训模式"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["modeRef"].validate(valid => {
    if (valid) {
      if (form.value.modeId != null) {
        updateMode(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addMode(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _modeIds = row.modeId || ids.value
  const tipMsg =
    ids.value.length == 0
      ? `是否确认删除"${row.modeName}"?`
      : "是否确认删除所选数据？";
  proxy.$modal.confirm(tipMsg).then(function() {
    return delMode(_modeIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('project/mode/export', {
    ...queryParams.value
  }, `mode_${new Date().getTime()}.xlsx`)
}

getList()
</script>
