<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="资源名称" prop="resourceName">
        <el-input
          v-model="queryParams.resourceName"
          placeholder="请输入资源名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="资源类别" prop="resourceCategory">
        <el-select
          v-model="queryParams.resourceCategory"
          placeholder="请选择资源类别"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in resource_category"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['project:resource:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['project:resource:remove']"
          >批量删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['project:resource:import']"
          >导入</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      border
      :data="resourceList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="80" align="center" />
      <el-table-column label="资源名称" align="center" prop="resourceName" />
      <el-table-column label="资源类别" align="center" prop="resourceCategory">
        <template #default="scope">
          <dict-tag
            :options="resource_category"
            :value="scope.row.resourceCategory"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="资源附件路径"
        align="center"
        prop="resourceAttachment"
      />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['project:resource:edit']"
            >编辑</el-button
          >
          <el-button
            link
            type="primary"
            @click="handleDelete(scope.row)"
            v-hasPermi="['project:resource:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改资源对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form
        ref="resourceRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="资源名称"
              prop="resourceName"
              label-position="top"
            >
              <el-input
                v-model="form.resourceName"
                placeholder="请输入资源名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="资源类别"
              prop="resourceCategory"
              label-position="top"
            >
              <el-select
                v-model="form.resourceCategory"
                placeholder="请选择资源类别"
              >
                <el-option
                  v-for="dict in resource_category"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item
          label="资源附件路径"
          prop="resourceAttachment"
          label-position="top"
        >
          <file-upload v-model="form.resourceAttachment" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Resource">
import {
  listResource,
  getResource,
  delResource,
  addResource,
  updateResource,
} from "@/api/project/resource";

const { proxy } = getCurrentInstance();
const { resource_category } = proxy.useDict("resource_category");

const resourceList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    resourceName: null,
    resourceCategory: null,
  },
  rules: {
    resourceName: [
      { required: true, message: "请输入资源名称！", trigger: "blur" },
    ],
    resourceCategory: [
      { required: true, message: "请选择资源类别！", trigger: "change" },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询资源列表 */
function getList() {
  loading.value = true;
  listResource(queryParams.value).then((response) => {
    resourceList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    resourceId: null,
    resourceName: null,
    resourceCategory: null,
    resourceAttachment: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
  };
  proxy.resetForm("resourceRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.resourceId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加资源";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _resourceId = row.resourceId || ids.value;
  getResource(_resourceId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改资源";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["resourceRef"].validate((valid) => {
    if (valid) {
      if (form.value.resourceId != null) {
        updateResource(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addResource(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _resourceIds = row.resourceId || ids.value;
  const tipMsg =
    ids.value.length == 0
      ? `是否确认删除"${row.resourceName}"?`
      : "是否确认删除所选数据？";
  proxy.$modal
    .confirm(tipMsg)
    .then(function () {
      return delResource(_resourceIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "project/resource/export",
    {
      ...queryParams.value,
    },
    `resource_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
