<template>
  <div class="evaluation-detail">
    <!-- 头部信息 -->
    <div class="detail-header">
      <div class="header-left">
        <h3>评价详情</h3>
        <div class="record-info">
          <span class="record-name">{{ selectedRecord?.recordName }}</span>
          <span class="record-status" :class="selectedRecord?.status === '1' ? 'completed' : 'in-progress'">
            {{ selectedRecord?.status === '1' ? '已完成' : '进行中' }}
          </span>
        </div>
      </div>
      <div class="header-right">
        <el-button v-if="editMode" type="success" @click="saveAnswers" :loading="saving">
          保存修改
        </el-button>
      </div>
    </div>



    <!-- 题目答案展示 -->
    <div class="answers-container" v-if="selectedRecord && answerList.length > 0">
      <div class="answers-list">
        <div
          v-for="(answer, index) in answerList"
          :key="answer.answerId"
          class="answer-item"
          :class="{ 'editing': editMode && hasModifiedAnswer(answer) }"
        >
          <!-- 题目标题行 -->
          <div class="question-title">
            <span class="question-number"># {{ index + 1 }}</span>
            <span class="question-text">{{ answer.questionStem }}</span>
          </div>

          <!-- 选项行 -->
          <div class="question-options">
            <div
              v-for="(option, optionIndex) in getQuestionOptions(answer.options)"
              :key="optionIndex"
              class="option-item"
            >
              <el-radio
                v-if="answer.questionType === '0'"
                :model-value="isOptionSelected(answer, optionIndex)"
                :label="true"
                :disabled="!editMode"
                @change="selectOption(answer, optionIndex, true)"
              >
              {{ option }}
              </el-radio>
              <el-checkbox
                v-else
                :model-value="isOptionSelected(answer, optionIndex)"
                :disabled="!editMode"
                @change="selectOption(answer, optionIndex, $event)"
              >
              {{ option }}
              </el-checkbox>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!loading" class="empty-state">
      <el-empty description="暂无评价记录" />
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <el-skeleton :rows="3" animated />
    </div>
  </div>
</template>

<script>
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getEvaluationRecordAnswers,
  submitAnswers
} from '@/api/training/evaluationRecord'

export default {
  name: 'EvaluationRecordDetail',
  props: {
    student: {
      type: Object,
      required: true
    },
    selectedRecord: {
      type: Object,
      default: null
    },
    editMode: {
      type: Boolean,
      default: false
    }
  },
  emits: ['edit-mode-change'],
  setup(props, { emit }) {
    // 响应式数据
    const loading = ref(false)
    const saving = ref(false)
    const answerList = ref([])
    const originalAnswers = ref({}) // 保存原始答案，用于取消编辑时恢复
    const modifiedAnswers = ref({}) // 修改后的答案

    // 计算属性
    const editMode = computed(() => props.editMode)

    // 方法

    const loadAnswerDetails = async (recordId) => {
      try {
        const response = await getEvaluationRecordAnswers(recordId)
        answerList.value = response.data || response.rows || []
        
        // 保存原始答案
        originalAnswers.value = {}
        modifiedAnswers.value = {}
        answerList.value.forEach(answer => {
          originalAnswers.value[answer.answerId] = answer.userAnswer
          modifiedAnswers.value[answer.answerId] = answer.userAnswer
        })
      } catch (error) {
        console.error('加载答题详情失败:', error)
        ElMessage.error('加载答题详情失败')
      }
    }



    const getQuestionOptions = (optionsStr) => {
      try {
        return JSON.parse(optionsStr)
      } catch (error) {
        return []
      }
    }

    const getOptionLabel = (index) => {
      return String.fromCharCode(65 + index) // A, B, C, D...
    }

    const isOptionSelected = (answer, optionIndex) => {
      const userAnswer = props.editMode ?
        modifiedAnswers.value[answer.answerId] :
        answer.userAnswer
      
      if (!userAnswer) return false
      
      const optionLabel = getOptionLabel(optionIndex)
      return userAnswer.includes(optionLabel)
    }

    const selectOption = (answer, optionIndex, checked) => {
      if (!props.editMode) return

      const optionLabel = getOptionLabel(optionIndex)
      let currentAnswer = modifiedAnswers.value[answer.answerId] || ''

      if (answer.questionType === '0') {
        // 单选题：直接设置为当前选项
        modifiedAnswers.value[answer.answerId] = checked ? optionLabel : ''
      } else {
        // 多选题：添加或移除选项
        const selectedOptions = currentAnswer ? currentAnswer.split(',') : []
        const optionIndex = selectedOptions.indexOf(optionLabel)

        if (checked && optionIndex === -1) {
          selectedOptions.push(optionLabel)
        } else if (!checked && optionIndex > -1) {
          selectedOptions.splice(optionIndex, 1)
        }

        modifiedAnswers.value[answer.answerId] = selectedOptions.join(',')
      }
    }

    const hasModifiedAnswer = (answer) => {
      const original = originalAnswers.value[answer.answerId] || ''
      const modified = modifiedAnswers.value[answer.answerId] || ''
      return original !== modified
    }

    const saveAnswers = async () => {
      try {
        saving.value = true
        
        // 准备提交的答案数据
        const submitData = answerList.value.map(answer => ({
          answerId: answer.answerId,
          recordId: answer.recordId,
          questionId: answer.questionId,
          userAnswer: modifiedAnswers.value[answer.answerId] || ''
        }))
        
        await submitAnswers(props.selectedRecord.recordId, submitData)
        
        // 更新本地数据
        answerList.value.forEach(answer => {
          answer.userAnswer = modifiedAnswers.value[answer.answerId]
          originalAnswers.value[answer.answerId] = answer.userAnswer
        })

        emit('edit-mode-change', false)
        ElMessage.success('答案修改成功')
      } catch (error) {
        console.error('保存答案失败:', error)
        ElMessage.error('保存答案失败')
      } finally {
        saving.value = false
      }
    }

    // 监听编辑模式变化
    watch(editMode, (newMode) => {
      if (!newMode) {
        // 退出编辑模式时，恢复原始答案
        modifiedAnswers.value = { ...originalAnswers.value }
      }
    })

    // 监听选中记录变化
    watch(() => props.selectedRecord, (newRecord) => {
      if (newRecord && newRecord.recordId) {
        loadAnswerDetails(newRecord.recordId)
        // 不自动退出编辑模式，让父组件控制
      } else {
        answerList.value = []
      }
    }, { immediate: true })

    // 监听编辑模式变化
    watch(() => props.editMode, (newMode) => {
      if (!newMode) {
        // 退出编辑模式时，恢复原始答案
        modifiedAnswers.value = { ...originalAnswers.value }
      }
    })

    return {
      loading,
      saving,
      editMode,
      answerList,
      getQuestionOptions,
      getOptionLabel,
      isOptionSelected,
      selectOption,
      hasModifiedAnswer,
      saveAnswers
    }
  }
}
</script>

<style scoped>
.evaluation-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}

.header-left h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #333;
}

.record-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.record-name {
  font-size: 14px;
  color: #666;
}

.record-status {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.record-status.completed {
  background: #f0f9ff;
  color: #1890ff;
}

.record-status.in-progress {
  background: #fff7e6;
  color: #fa8c16;
}



.answers-container {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  background: #fff;
}

.answer-item {
  margin-bottom: 24px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.answer-item:last-child {
  border-bottom: none;
}

.answer-item.editing {
  border: 1px solid #409eff;
  border-radius: 6px;
  padding: 16px;
  background: #f0f8ff;
}

.question-title {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  line-height: 1.5;
}

.question-number {
  font-size: 16px;
  font-weight: 500;
  color: #409eff;
  margin-right: 8px;
  flex-shrink: 0;
}

.question-text {
  font-size: 16px;
  color: #333;
  line-height: 1.5;
  flex: 1;
}

.question-options {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  padding-left: 32px; /* 对齐题目编号 */
}

.option-item {
  display: flex;
  align-items: center;
  min-width: 120px;
}

.option-item :deep(.el-radio),
.option-item :deep(.el-checkbox) {
  margin-right: 0;
}

.option-item :deep(.el-radio__label),
.option-item :deep(.el-checkbox__label) {
  font-size: 14px;
  color: #666;
}

.empty-state,
.loading-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}
</style>
