<template>
  <div class="answer-evaluation">
    <!-- 题目选择界面 -->
    <div v-if="!isAnswering" class="question-selection">
      <!-- 答题头部 -->
      <div class="answer-header">
        <div class="record-count">已选：{{ answeredCount }}</div>
        <!-- <el-button type="text" @click="$emit('close')" class="close-btn">
          <el-icon><Close /></el-icon>
        </el-button> -->
      </div>

      <!-- 题目列表 -->
      <div class="question-list">
        <div class="list-header">
          <div class="col-checkbox">
            <el-checkbox
              :model-value="isAllSelected"
              :indeterminate="isIndeterminate"
              @change="toggleSelectAll"
            >
            </el-checkbox>
          </div>
          <div class="col-number">#</div>
          <div class="col-title">题干</div>
          <div class="col-type">题目类型</div>
          <div class="col-status">选项</div>
        </div>

        <div class="list-body">
          <div
            v-for="(question, index) in questions"
            :key="question.questionId"
            :class="['question-row', {
              'selected': selectedQuestions.includes(question.questionId)
            }]"
          >
            <div class="col-checkbox">
              <el-checkbox
                :model-value="selectedQuestions.includes(question.questionId)"
                @change="toggleQuestion(question.questionId)"
              />
            </div>
            <div class="col-number">{{ index + 1 }}</div>
            <div class="col-title">{{ question.questionStem }}</div>
            <div class="col-type">{{ question.questionType === '0' ? '单选题' : '多选题' }}</div>
            <div class="col-status">{{ formatOptions(question.options) }}</div>
          </div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div class="load-more-section" v-if="!isAnswering">
        <div class="load-more-button" v-if="hasMoreQuestions">
          <el-button type="text" @click="loadMoreQuestions" class="load-more-btn">
            <span>+ 加载更多</span>
          </el-button>
        </div>
      </div>

      <!-- 底部操作 -->
      <div class="footer-actions">
        <el-button @click="startAnswering" type="primary" :disabled="selectedQuestions.length === 0">
          开始答题
        </el-button>
        <el-button @click="$emit('close')">取消</el-button>
      </div>
    </div>

    <!-- 答题界面 -->
    <div v-else class="answering-interface">
      <!-- 主要内容区域 - 左右布局 -->
      <div class="answering-content">
        <!-- 左侧题目导航 -->
        <div class="left-nav">
          <div class="nav-items">
            <div
              v-for="(question, index) in questions"
              :key="question.questionId"
              :class="['nav-item', {
                'active': index === currentQuestionIndex,
                'answered': userAnswers[question.questionId] && userAnswers[question.questionId].length > 0
              }]"
              @click="goToQuestion(index)"
            >
              <div class="nav-content">
                <span class="nav-number"># {{ index + 1 }}</span>
                <span class="nav-title">{{ question.questionStem }}</span>
              </div>
              <div class="nav-status">
                <el-icon v-if="userAnswers[question.questionId] && userAnswers[question.questionId].length > 0" class="check-icon">
                  ✓
                </el-icon>
                <span v-else class="dots">...</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧题目内容 -->
        <div class="right-content">
          <div v-if="currentQuestion" class="question-detail">
            <div class="question-header">
              <span class="question-number"># {{ currentQuestionIndex + 1 }}</span>
              <span class="question-type">{{ currentQuestion.questionType === '0' ? '单选' : '多选' }}</span>
            </div>

            <div class="question-text">
              {{ currentQuestion.questionStem }}
            </div>

            <div class="question-options">
              <div
                v-for="(option, index) in getQuestionOptions(currentQuestion.options)"
                :key="index"
                :class="['option-item', {
                  'selected': isOptionSelected(currentQuestion.questionId, index)
                }]"
                @click="handleOptionClick(currentQuestion, index, $event)"
              >
                <div class="option-radio">
                  <el-radio
                    v-if="currentQuestion.questionType === '0'"
                    :model-value="isOptionSelected(currentQuestion.questionId, index)"
                    :label="true"
                    @change="() => selectAnswer(currentQuestion.questionId, index)"
                  >
                  <div class="option-text">{{ option }}</div>
                  </el-radio>
                  <el-checkbox
                    v-else
                    :model-value="isOptionSelected(currentQuestion.questionId, index)"
                    @change="(checked) => selectAnswer(currentQuestion.questionId, index, checked)"
                  >
                  <div class="option-text">{{ option }}</div>
                  </el-checkbox>
                </div>
               
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作 -->
      <div class="footer-actions">
        <el-button type="primary" @click="completeAnswering">完成填写</el-button>
        <el-button @click="backToSelection">上一步</el-button>
        <el-button @click="cancelAnswering">取消</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Close } from '@element-plus/icons-vue'
import { listEvaluationQuestion, createEvaluationRecord, submitAnswers } from '@/api/training/evaluationRecord'

export default {
  name: 'AnswerEvaluation',
  components: {
    Close
  },
  props: {
    record: {
      type: Object,
      required: true
    }
  },
  emits: ['close', 'success'],
  setup(props, { emit }) {
    // 响应式数据
    const questions = ref([])
    const allQuestions = ref([]) // 存储所有题目
    const displayCount = ref(1) // 当前显示的题目数量
    const selectedQuestions = ref([])
    const isAnswering = ref(false) // 是否在答题状态
    const currentQuestionIndex = ref(0) // 当前题目索引
    const userAnswers = ref({}) // 用户答案

    // 计算属性
    const answeredCount = computed(() => {
      if (isAnswering.value) {
        return Object.keys(userAnswers.value).length
      }
      return selectedQuestions.value.length
    })

    const currentQuestion = computed(() => {
      if (isAnswering.value && questions.value.length > 0) {
        return questions.value[currentQuestionIndex.value]
      }
      return null
    })

    const selectedQuestionsForAnswering = computed(() => {
      return questions.value.filter(q => selectedQuestions.value.includes(q.questionId))
    })

    // 全选相关计算属性
    const isAllSelected = computed(() => {
      return questions.value.length > 0 && selectedQuestions.value.length === questions.value.length
    })

    const isIndeterminate = computed(() => {
      return selectedQuestions.value.length > 0 && selectedQuestions.value.length < questions.value.length
    })

    // 当前显示的题目列表（用于加载更多功能）
    const displayedQuestions = computed(() => {
      return allQuestions.value.slice(0, displayCount.value)
    })

    // 是否还有更多题目可以加载
    const hasMoreQuestions = computed(() => {
      return displayCount.value < allQuestions.value.length
    })

    // 方法
    const loadQuestions = async () => {
      try {
        if (!props.record?.bankId) {
          return
        }

        // 加载题库中的题目
        const response = await listEvaluationQuestion({ bankId: props.record.bankId })
        allQuestions.value = response.rows || []
        // 重置显示数量为10
        displayCount.value = 10
        // 更新当前显示的题目
        questions.value = displayedQuestions.value
      } catch (error) {
        console.error('加载题目失败:', error)
        ElMessage.error('加载题目失败')
      }
    }

    const toggleQuestion = (questionId) => {
      const index = selectedQuestions.value.indexOf(questionId)
      if (index > -1) {
        selectedQuestions.value.splice(index, 1)
      } else {
        selectedQuestions.value.push(questionId)
      }
    }

    const toggleSelectAll = (checked) => {
      if (checked) {
        // 全选：选择所有题目
        selectedQuestions.value = questions.value.map(q => q.questionId)
      } else {
        // 取消全选：清空选择
        selectedQuestions.value = []
      }
    }

    const formatOptions = (optionsStr) => {
      try {
        const options = JSON.parse(optionsStr)
        return options.map((option, index) => {
          const label = String.fromCharCode(65 + index) // A, B, C, D...
          return `${label}. ${option}`
        }).join('; ')
      } catch (error) {
        return optionsStr || ''
      }
    }

    const getQuestionOptions = (optionsStr) => {
      try {
        return JSON.parse(optionsStr)
      } catch (error) {
        return []
      }
    }

    const selectAnswer = (questionId, optionIndex, checked) => {
      console.log('selectAnswer called:', { questionId, optionIndex, checked })
      const question = questions.value.find(q => q.questionId === questionId)
      if (!question) return

      console.log('question type:', question.questionType)

      if (question.questionType === '0') { // 单选
        userAnswers.value[questionId] = [optionIndex]
      } else { // 多选
        if (!userAnswers.value[questionId]) {
          userAnswers.value[questionId] = []
        }
        const answers = userAnswers.value[questionId]
        const index = answers.indexOf(optionIndex)

        console.log('current answers:', answers, 'optionIndex:', optionIndex, 'index:', index)

        // 如果传入了 checked 参数（来自 checkbox），使用它来决定是否选中
        if (checked !== undefined) {
          console.log('using checked parameter:', checked)
          if (checked && index === -1) {
            answers.push(optionIndex)
            console.log('added option, new answers:', answers)
          } else if (!checked && index > -1) {
            answers.splice(index, 1)
            console.log('removed option, new answers:', answers)
          }
        } else {
          // 如果没有 checked 参数（来自点击事件），切换选中状态
          console.log('toggling option')
          if (index > -1) {
            answers.splice(index, 1)
          } else {
            answers.push(optionIndex)
          }
          console.log('new answers after toggle:', answers)
        }
      }
    }

    const handleOptionClick = (question, optionIndex, event) => {
      // 对于多选题，如果点击的是 checkbox 本身，不处理（让 checkbox 的 change 事件处理）
      if (question.questionType === '1' && event.target.closest('.el-checkbox')) {
        return
      }
      // 对于单选题或点击选项文本区域，直接处理
      selectAnswer(question.questionId, optionIndex)
    }

    const isOptionSelected = (questionId, optionIndex) => {
      const answers = userAnswers.value[questionId]
      return answers && answers.includes(optionIndex)
    }

    const goToNextQuestion = () => {
      if (currentQuestionIndex.value < questions.value.length - 1) {
        currentQuestionIndex.value++
      }
    }

    const goToPrevQuestion = () => {
      if (currentQuestionIndex.value > 0) {
        currentQuestionIndex.value--
      }
    }

    const goToQuestion = (index) => {
      currentQuestionIndex.value = index
    }

    const completeAnswering = async () => {
      try {
        // 检查是否有未答的题目
        const unansweredQuestions = questions.value.filter(q =>
          !userAnswers.value[q.questionId] || userAnswers.value[q.questionId].length === 0
        )

        if (unansweredQuestions.length > 0) {
          ElMessage.warning(`还有 ${unansweredQuestions.length} 道题目未答，请完成所有题目后再提交`)
          return
        }

        // 第一步：如果还没有创建评价记录，先创建
        let recordId = props.record.recordId

        if (!recordId) {
          // 创建评价记录
          const createData = {
            studentId: props.record.studentId,
            bankId: props.record.bankId,
            questionIds: selectedQuestions.value
          }

          const createResponse = await createEvaluationRecord(createData)
          recordId = createResponse.data.recordId

          ElMessage.success('评价记录创建成功')
        }

        // 第二步：准备答题数据
        const answerList = []
        questions.value.forEach(question => {
          const userAnswer = userAnswers.value[question.questionId]
          if (userAnswer && userAnswer.length > 0) {
            // 将答案数组转换为字符串格式
            let answerStr = ''
            if (question.questionType === '0') {
              // 单选题：直接取第一个答案的索引，转换为选项字母
              answerStr = String.fromCharCode(65 + userAnswer[0]) // 0->A, 1->B, 2->C, 3->D
            } else {
              // 多选题：将所有答案索引转换为选项字母，用逗号分隔
              answerStr = userAnswer.map(index => String.fromCharCode(65 + index)).join(',')
            }

            answerList.push({
              recordId: recordId,
              questionId: question.questionId,
              questionStem: question.questionStem,
              questionType: question.questionType,
              options: question.options,
              userAnswer: answerStr
            })
          }
        })

        // 第三步：提交答题记录
        await submitAnswers(recordId, answerList)

        ElMessage.success('答题提交成功！')

        // 发送成功事件，传递记录ID以便父组件刷新列表
        emit('success', { recordId, action: 'complete' })

      } catch (error) {
        console.error('保存答题结果失败:', error)
        ElMessage.error('保存答题结果失败：' + (error.message || '未知错误'))
      }
    }

    const cancelAnswering = () => {
      // 只重置答题相关状态，不清空题目列表
      selectedQuestions.value = []
      isAnswering.value = false
      currentQuestionIndex.value = 0
      userAnswers.value = {}
      emit('close')
    }

    const backToSelection = () => {
      isAnswering.value = false
      currentQuestionIndex.value = 0
      userAnswers.value = {}
      // 重新加载全部题目，而不是只显示已选择的
      forceRefreshQuestions()
    }

    const resetAllStates = () => {
      // 重置所有状态到初始值，但不清空 questions，因为需要重新加载
      selectedQuestions.value = []
      isAnswering.value = false
      currentQuestionIndex.value = 0
      userAnswers.value = {}
      displayCount.value = 1
    }

    // 加载更多题目
    const loadMoreQuestions = () => {
      const increment = 10 // 每次加载10道题目
      const newCount = Math.min(displayCount.value + increment, allQuestions.value.length)
      displayCount.value = newCount
    }

    const startAnswering = () => {
      if (selectedQuestions.value.length === 0) {
        ElMessage.warning('请至少选择一道题目')
        return
      }

      // 进入答题状态
      isAnswering.value = true
      currentQuestionIndex.value = 0
      userAnswers.value = {}

      // 只保留选中的题目用于答题
      questions.value = questions.value.filter(q => selectedQuestions.value.includes(q.questionId))
    }

    // 强制刷新题目列表的方法
    const forceRefreshQuestions = () => {
      if (props.record && props.record.bankId) {
        // 重置所有状态
        selectedQuestions.value = []
        isAnswering.value = false
        currentQuestionIndex.value = 0
        userAnswers.value = {}
        // 强制重新加载题目
        loadQuestions()
      }
    }

    // 生命周期
    onMounted(() => {
      if (props.record && props.record.bankId) {
        loadQuestions()
      }
    })

    // 监听显示数量变化，更新题目列表
    watch(displayCount, () => {
      questions.value = displayedQuestions.value
    })

    // 监听记录变化 - 简化逻辑，只在有效记录时加载
    watch(() => props.record, (newRecord, oldRecord) => {
      if (newRecord && newRecord.bankId) {
        // 如果是新记录或bankId发生变化，重新加载
        if (!oldRecord || oldRecord.bankId !== newRecord.bankId || oldRecord === null) {
          // 重置选择和答题状态
          selectedQuestions.value = []
          isAnswering.value = false
          currentQuestionIndex.value = 0
          userAnswers.value = {}
          displayCount.value = 1
          // 重新加载题目
          loadQuestions()
        }
      }
    }, { immediate: true, deep: true })

    return {
      questions,
      allQuestions,
      displayCount,
      selectedQuestions,
      answeredCount,
      isAnswering,
      currentQuestionIndex,
      currentQuestion,
      userAnswers,
      displayedQuestions,
      hasMoreQuestions,
      isAllSelected,
      isIndeterminate,
      toggleQuestion,
      toggleSelectAll,
      startAnswering,
      loadMoreQuestions,
      formatOptions,
      getQuestionOptions,
      selectAnswer,
      handleOptionClick,
      isOptionSelected,
      goToNextQuestion,
      goToPrevQuestion,
      goToQuestion,
      completeAnswering,
      cancelAnswering,
      backToSelection,
      resetAllStates,
      forceRefreshQuestions,
      Close
    }
  }
}
</script>

<style scoped>
.answer-evaluation {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.answer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.header-left h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.record-count {
  /* font-size: 12px; */
  color: #666;
}

.close-btn {
  color: #999;
  padding: 4px;
}

.question-list {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.list-header {
  display: flex;
  padding: 12px 20px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.col-checkbox {
  width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.col-number {
  width: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #666;
}

.col-title {
  flex: 1;
  padding: 0 16px;
}

.col-type {
  width: 100px;
  text-align: center;
}

.col-status {
  width: 40%;
  padding: 0 16px;
  color: #666;
}

.list-body {
  flex: 1;
  overflow-y: auto;
}

.question-row {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.question-row:hover {
  background-color: #f8f9fa;
}

.question-row.selected {
  background-color: #e3f2fd;
}



.question-row .col-title {
  font-size: 14px;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.question-row .col-type {
  font-size: 14px;
  color: #666;
}

.load-more-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.load-more-info {
  color: #666;
  font-size: 14px;
  margin-bottom: 12px;
}

.load-more-button {
  width: 100%;
  display: flex;
  justify-content: center;
}

.load-more-btn {
  padding: 12px 24px;
  font-size: 14px;
  color: #409eff;
  border: 1px dashed #409eff;
  border-radius: 6px;
  background: #fff;
  transition: all 0.3s;
  min-width: 120px;
}

.load-more-btn:hover {
  background: #f0f8ff;
  border-color: #66b1ff;
  color: #66b1ff;
}

.footer-actions {
  padding: 16px 20px;
  border-top: 1px solid #e0e0e0;
  text-align: right;
  background: #fff;
}

.footer-actions .el-button {
  margin-left: 8px;
}

/* 答题头部 */
.answer-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.progress-info h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.progress-info p {
  margin: 0 0 16px 0;
  color: #666;
  font-size: 14px;
}

.progress-bar {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-bar .el-progress {
  flex: 1;
}

.progress-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  min-width: 60px;
  text-align: right;
}

/* 题目导航 */
.question-nav {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}

.nav-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.nav-item {
  
  height: 32px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s;
}

.nav-item:hover {
  border-color: #2196f3;
  color: #2196f3;
}

/* 题目容器 */
.question-container {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.question-header {
  margin-bottom: 24px;
}

.question-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.question-number {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.question-content {
  max-width: 800px;
}

.question-stem {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 24px;
  color: #333;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

.question-options {
  margin-top: 24px;
}

.radio-group,
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.radio-option,
.checkbox-option {
  margin: 0;
  padding: 16px;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  transition: all 0.2s;
  background: #fff;
}

.radio-option:hover,
.checkbox-option:hover {
  border-color: #2196f3;
  background: #f0f8ff;
}

.radio-option.is-checked,
.checkbox-option.is-checked {
  border-color: #2196f3;
  background: #f0f8ff;
}

.option-content {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  width: 100%;
}

.option-label {
  font-weight: 600;
  color: #2196f3;
  min-width: 24px;
}

.option-text {
  flex: 1;
  line-height: 1.5;
  color: #333;
}

/* 答题操作 */
.answer-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.action-left,
.action-right {
  display: flex;
  gap: 12px;
}

.action-center {
  font-size: 14px;
  color: #666;
}

.answered-count {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .answer-header {
    padding: 16px;
  }
  
  .question-nav {
    padding: 12px 16px;
  }
  
  .nav-items {
    gap: 6px;
  }
  
  .nav-item {
    width: 28px;
    height: 28px;
    font-size: 11px;
  }
  
  .question-container {
    padding: 16px;
  }
  
  .question-stem {
    padding: 16px;
    font-size: 15px;
  }
  
  .answer-actions {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }
  
  .action-left,
  .action-right {
    width: 100%;
    justify-content: center;
  }
}

/* 答题界面样式 */
.answering-interface {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.answering-content {
  flex: 1;
  display: flex;
  min-height: 0;
}

.left-nav {
  width: 280px;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  height: 100%;
}



.nav-items {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.nav-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 1px solid #e8e8e8;
  min-height: 60px;
}

.nav-item:hover {
  background: rgba(64, 158, 255, 0.05);
}

.nav-item.active {
  background: rgba(64, 158, 255, 0.1);
}

.nav-content {
  display: flex;
  align-items: flex-start;
  flex: 1;
  min-width: 0;
}

.nav-number {
  color: #409eff;
  font-size: 16px;
  font-weight: 500;
  margin-right: 8px;
  flex-shrink: 0;
}

.nav-title {
  flex: 1;
  font-size: 14px;
  color: #333;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.nav-status {
  flex-shrink: 0;
  margin-left: 12px;
}

.check-icon {
  color: #67c23a;
  font-size: 18px;
}

.dots {
  color: #ccc;
  font-size: 16px;
  font-weight: bold;
}

.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.question-detail {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.question-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.question-number {
  font-size: 18px;
  font-weight: 500;
  color: #409eff;
}

.question-type {
  padding: 4px 8px;
  background: #e1f3ff;
  color: #409eff;
  border-radius: 4px;
  font-size: 12px;
}

.question-text {
  font-size: 16px;
  line-height: 1.6;
  color: #333;
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.question-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.option-item:hover {
  border-color: #409eff;
  background: #f0f8ff;
}

.option-item.selected {
  border-color: #409eff;
  background: #e1f3ff;
}

.option-radio {
  margin-right: 12px;
  flex-shrink: 0;
}

.option-text {
  flex: 1;
  line-height: 1.5;
  color: #333;
}
</style>
