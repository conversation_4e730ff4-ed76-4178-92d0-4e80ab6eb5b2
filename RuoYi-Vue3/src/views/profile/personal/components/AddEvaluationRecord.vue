<template>
  <div class="add-evaluation-record">
    <!-- 步骤条 -->
    <div class="steps-container">
      <el-steps :active="currentStep" align-center>
        <el-step title="选择评价模型" />
        <el-step title="选择题目" />
        <el-step title="开始评价" />
      </el-steps>
    </div>

    <!-- 步骤1: 选择评价模型 -->
    <div v-if="currentStep === 0" class="step-content">
      <div class="step-header">
        <h3>选择评价模型</h3>
        <p>请选择适合的评价模型进行评价</p>
      </div>
      
      <div class="model-grid" v-loading="bankLoading">
        <div 
          v-for="bank in bankList" 
          :key="bank.bankId"
          :class="['model-card', { 'selected': selectedBankId === bank.bankId }]"
          @click="selectBank(bank)"
        >
          <div class="model-icon">
            <el-icon size="32"><Document /></el-icon>
          </div>
          <div class="model-info">
            <h4>{{ bank.bankName }}</h4>
            <p>{{ bank.remark || '暂无描述' }}</p>
          </div>
          <div class="model-status">
            <el-tag v-if="bank.status === '0'" type="success" size="small">可用</el-tag>
            <el-tag v-else type="danger" size="small">停用</el-tag>
          </div>
        </div>
      </div>

      <div class="step-actions">
        <el-button @click="$emit('close')">取消</el-button>
        <el-button 
          type="primary" 
          @click="nextStep"
          :disabled="!selectedBankId"
        >
          下一步
        </el-button>
      </div>
    </div>

    <!-- 步骤2: 选择题目 -->
    <div v-if="currentStep === 1" class="step-content">
      <div class="step-header">
        <h3>选择题目</h3>
        <p>从 {{ selectedBank?.bankName }} 中选择要评价的题目</p>
      </div>

      <div class="question-selection">
        <div class="selection-toolbar">
          <div class="selection-info">
            <span>已选择 {{ selectedQuestions.length }} 道题目</span>
          </div>
          <div class="selection-actions">
            <el-button size="small" @click="selectAll">全选</el-button>
            <el-button size="small" @click="clearAll">清空</el-button>
          </div>
        </div>

        <div class="question-list" v-loading="questionLoading">
          <div 
            v-for="(question, index) in questionList" 
            :key="question.questionId"
            :class="['question-card', { 'selected': question.selected }]"
            @click="toggleQuestion(question)"
          >
            <div class="question-header">
              <div class="question-meta">
                <span class="question-number">{{ index + 1 }}</span>
                <el-tag 
                  :type="question.questionType === '0' ? 'primary' : 'success'" 
                  size="small"
                >
                  {{ question.questionType === '0' ? '单选题' : '多选题' }}
                </el-tag>
              </div>
              <el-checkbox 
                v-model="question.selected"
                @click.stop
                @change="handleQuestionSelect(question)"
              />
            </div>
            
            <div class="question-content">
              <div class="question-stem">{{ question.questionStem }}</div>
              <div class="question-options">
                <div 
                  v-for="(option, optionIndex) in parseOptions(question.options)" 
                  :key="optionIndex"
                  class="option-item"
                >
                  <span class="option-label">{{ getOptionLabel(optionIndex) }}.</span>
                  <span class="option-text">{{ option }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="step-actions">
        <el-button @click="prevStep">上一步</el-button>
        <el-button @click="$emit('close')">取消</el-button>
        <el-button 
          type="primary" 
          @click="nextStep"
          :disabled="selectedQuestions.length === 0"
        >
          下一步
        </el-button>
      </div>
    </div>

    <!-- 步骤3: 开始评价 -->
    <div v-if="currentStep === 2" class="step-content">
      <div class="step-header">
        <h3>确认信息</h3>
        <p>请确认评价信息，点击开始评价进入答题</p>
      </div>

      <div class="confirm-info">
        <div class="info-card">
          <div class="info-item">
            <label>评价模型：</label>
            <span>{{ selectedBank?.bankName }}</span>
          </div>
          <div class="info-item">
            <label>题目数量：</label>
            <span>{{ selectedQuestions.length }} 道</span>
          </div>
          <div class="info-item">
            <label>预计用时：</label>
            <span>{{ Math.ceil(selectedQuestions.length * 2) }} 分钟</span>
          </div>
        </div>
      </div>

      <div class="step-actions">
        <el-button @click="prevStep">上一步</el-button>
        <el-button @click="$emit('close')">取消</el-button>
        <el-button 
          type="primary" 
          @click="startEvaluation"
          :loading="creating"
        >
          开始评价
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import { listBank } from '@/api/training/bank'
import { listEvaluationQuestion, createEvaluationRecord } from '@/api/training/evaluationRecord'

export default {
  name: 'AddEvaluationRecord',
  components: {
    Document
  },
  props: {
    student: {
      type: Object,
      required: true
    }
  },
  emits: ['close', 'success'],
  setup(props, { emit }) {
    // 响应式数据
    const currentStep = ref(0)
    const bankLoading = ref(false)
    const questionLoading = ref(false)
    const creating = ref(false)
    
    const bankList = ref([])
    const questionList = ref([])
    const selectedBankId = ref(null)
    const selectedQuestions = ref([])

    // 计算属性
    const selectedBank = computed(() => {
      return bankList.value.find(bank => bank.bankId === selectedBankId.value)
    })

    // 方法
    const loadBankList = async () => {
      bankLoading.value = true
      try {
        const response = await listBank({ status: '0' })
        bankList.value = response.data || []
      } catch (error) {
        console.error('加载题库列表失败:', error)
        ElMessage.error('加载题库列表失败')
      } finally {
        bankLoading.value = false
      }
    }

    const loadQuestionList = async () => {
      if (!selectedBankId.value) return
      
      questionLoading.value = true
      try {
        const response = await listEvaluationQuestion({
          bankId: selectedBankId.value,
          pageNum: 1,
          pageSize: 1000,
          status: '0'
        })
        
        questionList.value = (response.rows || []).map(q => ({
          ...q,
          selected: false
        }))
      } catch (error) {
        console.error('加载题目失败:', error)
        ElMessage.error('加载题目失败')
      } finally {
        questionLoading.value = false
      }
    }

    const selectBank = (bank) => {
      selectedBankId.value = bank.bankId
    }

    const toggleQuestion = (question) => {
      question.selected = !question.selected
      handleQuestionSelect(question)
    }

    const handleQuestionSelect = (question) => {
      if (question.selected) {
        if (!selectedQuestions.value.find(q => q.questionId === question.questionId)) {
          selectedQuestions.value.push(question)
        }
      } else {
        const index = selectedQuestions.value.findIndex(q => q.questionId === question.questionId)
        if (index > -1) {
          selectedQuestions.value.splice(index, 1)
        }
      }
    }

    const selectAll = () => {
      questionList.value.forEach(question => {
        if (!question.selected) {
          question.selected = true
          handleQuestionSelect(question)
        }
      })
    }

    const clearAll = () => {
      questionList.value.forEach(question => {
        question.selected = false
      })
      selectedQuestions.value = []
    }

    const nextStep = async () => {
      if (currentStep.value === 0) {
        await loadQuestionList()
      }
      currentStep.value++
    }

    const prevStep = () => {
      currentStep.value--
    }

    const startEvaluation = async () => {
      try {
        creating.value = true
        
        const createData = {
          studentId: props.student.studentId,
          bankId: selectedBankId.value,
          questionIds: selectedQuestions.value.map(q => q.questionId)
        }
        
        const response = await createEvaluationRecord(createData)
        
        ElMessage.success('评价记录创建成功')
        emit('success', response.data)
      } catch (error) {
        console.error('创建评价记录失败:', error)
        ElMessage.error('创建评价记录失败')
      } finally {
        creating.value = false
      }
    }

    const parseOptions = (optionsStr) => {
      try {
        return JSON.parse(optionsStr)
      } catch (error) {
        return []
      }
    }

    const getOptionLabel = (index) => {
      return String.fromCharCode(65 + index) // A, B, C, D...
    }

    // 生命周期
    onMounted(() => {
      loadBankList()
    })

    return {
      currentStep,
      bankLoading,
      questionLoading,
      creating,
      bankList,
      questionList,
      selectedBankId,
      selectedQuestions,
      selectedBank,
      selectBank,
      toggleQuestion,
      handleQuestionSelect,
      selectAll,
      clearAll,
      nextStep,
      prevStep,
      startEvaluation,
      parseOptions,
      getOptionLabel
    }
  }
}
</script>

<style scoped>
.add-evaluation-record {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.steps-container {
  margin-bottom: 32px;
}

.step-content {
  min-height: 500px;
}

.step-header {
  text-align: center;
  margin-bottom: 32px;
}

.step-header h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.step-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* 评价模型选择 */
.model-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.model-card {
  border: 2px solid #e5e5e5;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
  background: #fff;
}

.model-card:hover {
  border-color: #2196f3;
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15);
}

.model-card.selected {
  border-color: #2196f3;
  background: #f0f8ff;
}

.model-icon {
  text-align: center;
  margin-bottom: 16px;
  color: #2196f3;
}

.model-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.model-info p {
  margin: 0;
  font-size: 12px;
  color: #666;
  text-align: center;
  line-height: 1.4;
}

.model-status {
  text-align: center;
  margin-top: 12px;
}

/* 题目选择 */
.question-selection {
  margin-bottom: 32px;
}

.selection-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.selection-info {
  font-weight: 500;
  color: #333;
}

.selection-actions {
  display: flex;
  gap: 8px;
}

.question-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
}

.question-card {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.question-card:last-child {
  border-bottom: none;
}

.question-card:hover {
  background: #f8f9fa;
}

.question-card.selected {
  background: #f0f8ff;
  border-left: 3px solid #2196f3;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.question-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.question-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: #2196f3;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 600;
}

.question-stem {
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 12px;
  color: #333;
}

.question-options {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.option-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 13px;
  color: #666;
}

.option-label {
  font-weight: 500;
  min-width: 20px;
}

.option-text {
  flex: 1;
  line-height: 1.4;
}

/* 确认信息 */
.confirm-info {
  margin-bottom: 32px;
}

.info-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 24px;
  max-width: 400px;
  margin: 0 auto;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item label {
  font-weight: 500;
  color: #333;
}

.info-item span {
  color: #666;
}

/* 步骤操作 */
.step-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .add-evaluation-record {
    padding: 16px;
  }
  
  .model-grid {
    grid-template-columns: 1fr;
  }
  
  .selection-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .step-actions {
    flex-direction: column;
  }
}
</style>
