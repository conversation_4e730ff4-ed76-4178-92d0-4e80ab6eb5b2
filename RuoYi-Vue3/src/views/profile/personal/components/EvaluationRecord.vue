<template>
  <div class="evaluation-record-container">
    <!-- 评价记录列表 -->
    <div class="record-list">
      <div class="list-header">
        <h3>评价记录</h3>
        <el-button type="text" @click="showAddRecord = true" class="add-btn">
          <el-icon><Plus /></el-icon>
          添加
        </el-button>
      </div>

      <div class="record-items" v-loading="loading">
        <div v-if="!loading && recordList.length === 0" class="no-data">
          <el-empty description="暂无评价记录" />
        </div>

        <div
          v-for="record in recordList"
          :key="record.recordId"
          class="record-item"
          :class="{ 'selected': selectedRecordId === record.recordId }"
          @click="selectRecord(record)"
        >
          <div class="record-content">
            <div class="record-time">{{ record.recordName }}</div>
            <div class="record-status" :class="record.status === '1' ? 'completed' : 'in-progress'">
              {{ record.status === '1' ? '已完成' : '进行中' }}
            </div>
          </div>
          <div class="record-actions">
            <el-button
              type="text"
              @click="continueAnswer(record)"
              class="edit-btn"
            >
              <el-icon><Edit /></el-icon>
            </el-button>
            <el-button
              type="text"
              @click="deleteRecord(record)"
              class="delete-btn"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>

        <div v-if="recordList.length > 0" class="more-indicator">
          没有更多了~
        </div>
      </div>
    </div>

    <!-- 添加评价记录弹窗 -->
    <el-dialog
      v-if="showAddRecord"
      v-model="showAddRecord"
      title="添加评价记录"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="addForm" :rules="addRules" ref="addFormRef" label-width="80px">
        <el-form-item label="评价模型" prop="bankId" required>
          <el-select
            v-model="addForm.bankId"
            placeholder="请选择评价模型"
            style="width: 100%"
          >
            <el-option
              v-for="bank in bankList"
              :key="bank.bankId"
              :label="bank.bankName"
              :value="bank.bankId"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAddRecord = false">取消</el-button>
          <el-button type="primary" @click="confirmAdd" :loading="creating">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 答题弹窗 -->
    <el-dialog
      v-model="showAnswerDialog"
      title="添加评价记录"
      width="95%"
      :close-on-click-modal="false"
      :show-close="true"
      class="answer-dialog"
    >
      <AnswerEvaluation
        ref="answerEvaluationRef"
        :record="currentRecord"
        @close="handleAnswerClose"
        @success="handleAnswerSuccess"
      />
    </el-dialog>


  </div>
</template>

<script>
import { ref, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, View, Delete, Edit } from '@element-plus/icons-vue'
import {
  listEvaluationRecordByStudent,
  delEvaluationRecord,
  addEvaluationRecord
} from '@/api/training/evaluationRecord'
import { listBank } from '@/api/training/bank'
import AnswerEvaluation from './AnswerEvaluation.vue'

export default {
  name: 'EvaluationRecord',
  components: {
    Plus,
    View,
    Delete,
    Edit,
    AnswerEvaluation
  },
  props: {
    student: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['record-select', 'edit-record'],
  setup(props, { emit }) {
    // 响应式数据
    const loading = ref(false)
    const recordList = ref([])
    const showAddRecord = ref(false)
    const showAnswerDialog = ref(false)

    const creating = ref(false)

    // 当前记录
    const currentRecord = ref(null)
    const selectedRecordId = ref(null)

    // 子组件引用
    const answerEvaluationRef = ref(null)

    // 添加记录表单
    const addForm = ref({
      bankId: ''
    })
    const addFormRef = ref(null)
    const bankList = ref([])

    const addRules = {
      bankId: [
        { required: true, message: '请选择评价模型', trigger: 'change' }
      ]
    }

    // 方法
    const loadRecordList = async () => {
      if (!props.student?.studentId) return

      loading.value = true
      try {
        const response = await listEvaluationRecordByStudent(props.student.studentId)
        recordList.value = response.data || []

        // 默认选中最新的一条记录
        if (recordList.value.length > 0) {
          const sortedRecords = recordList.value.sort((a, b) =>
            new Date(b.createTime) - new Date(a.createTime)
          )
          selectedRecordId.value = sortedRecords[0].recordId
          emit('record-select', sortedRecords[0])
        }
      } catch (error) {
        console.error('加载评价记录失败:', error)
        ElMessage.error('加载评价记录失败')
      } finally {
        loading.value = false
      }
    }

    const selectRecord = (record) => {
      selectedRecordId.value = record.recordId
      emit('record-select', record)
    }

    const loadBankList = async () => {
      try {
        const response = await listBank({})
        bankList.value = response.data || []
      } catch (error) {
        console.error('加载题库列表失败:', error)
        ElMessage.error('加载题库列表失败')
      }
    }

    const confirmAdd = async () => {
      if (!addFormRef.value) return

      try {
        await addFormRef.value.validate()
        creating.value = true

        // 创建一个全新的记录对象，用于传递给题目选择界面
        const tempRecord = {
          recordId: null, // 新记录没有ID
          studentId: props.student.studentId,
          bankId: addForm.value.bankId,
          bankName: bankList.value.find(bank => bank.bankId === addForm.value.bankId)?.bankName || '',
          status: '0' // 进行中
        }

        showAddRecord.value = false
        addForm.value.bankId = ''

        // 进入题目选择界面
        currentRecord.value = tempRecord
        showAnswerDialog.value = true
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error('操作失败')
      } finally {
        creating.value = false
      }
    }

    const continueAnswer = (record) => {
      // 先选中记录
      selectedRecordId.value = record.recordId
      emit('record-select', record)

      // 然后触发编辑模式
      emit('edit-record', record)
    }

    const handleAnswerSuccess = (result) => {
      showAnswerDialog.value = false
      loadRecordList()

      if (result) {
        if (result.action === 'complete') {
          // 答题完成
          ElMessage.success('答题已完成！')
        } else if (result.recordId) {
          // 评价记录创建完成，可以继续答题
          ElMessage.success('题目选择完成，评价记录已创建')
        }
      }

      // 延迟重置当前记录，避免影响下次打开
      setTimeout(() => {
        currentRecord.value = null
      }, 100)
    }

    const handleAnswerClose = () => {
      showAnswerDialog.value = false
      // 延迟重置当前记录，避免影响下次打开
      setTimeout(() => {
        currentRecord.value = null
      }, 100)
    }

    const deleteRecord = async (record) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除评价记录"${record.recordName}"吗？`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        await delEvaluationRecord([record.recordId])
        ElMessage.success('删除成功')
        await loadRecordList()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error)
          ElMessage.error('删除失败')
        }
      }
    }



    const formatDate = (dateStr) => {
      if (!dateStr) return ''
      return new Date(dateStr).toLocaleString()
    }

    // 生命周期
    onMounted(() => {
      loadBankList()
    })

    // 监听学生变化
    watch(() => props.student, (newStudent) => {
      if (newStudent?.studentId) {
        loadRecordList()
      }
    }, { immediate: true })

    return {
      loading,
      recordList,
      showAddRecord,
      showAnswerDialog,
      creating,
      currentRecord,
      selectedRecordId,
      answerEvaluationRef,
      addForm,
      addFormRef,
      addRules,
      bankList,
      loadBankList,
      confirmAdd,
      continueAnswer,
      selectRecord,
      handleAnswerSuccess,
      handleAnswerClose,
      deleteRecord,
      formatDate,
      Plus,
      View,
      Delete,
      Edit
    }
  }
}
</script>

<style scoped>
.evaluation-record-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.record-list {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 0;
  min-height: 400px;
  flex: 1;
  overflow-y: auto;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.list-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.add-btn {
  color: #409eff;
  font-size: 14px;
  padding: 0;
}

.add-btn .el-icon {
  margin-right: 4px;
}

.record-items {
  min-height: 300px;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  margin-bottom: 1px;
  cursor: pointer;
  transition: all 0.2s;
}

.record-item:hover {
  background: #f0f8ff;
}

.record-item.selected {
  background: #e1f3ff;
  border-left: 3px solid #409eff;
}

.record-item:last-child {
  border-bottom: none;
}

.record-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.record-status {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  align-self: flex-start;
}

.record-status.completed {
  background: #f0f9ff;
  color: #1890ff;
}

.record-status.in-progress {
  background: #fff7e6;
  color: #fa8c16;
}

.record-time {
  font-size: 14px;
  color: #333;
  flex: 1;
}

.record-actions {
  display: flex;
  gap: 8px;
}

.edit-btn {
  color: #409eff;
  padding: 4px;
}

.delete-btn {
  color: #f56c6c;
  padding: 4px;
}

.more-indicator {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 14px;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

/* 弹窗样式 */
:deep(.add-record-dialog .el-dialog__body) {
  padding: 0;
}

:deep(.answer-dialog .el-dialog__body) {
  padding: 0;
  height: 80vh;
}

/* 查看记录样式 */
.view-content {
  max-height: 70vh;
  overflow-y: auto;
}

.record-summary {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  margin-bottom: 24px;
}

.record-summary h4 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.summary-item label {
  font-weight: 500;
  color: #666;
}

.summary-item span {
  color: #333;
}

.answer-details h5 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.answer-detail-item {
  border: 1px solid #e5e5e5;
  border-radius: 12px;
  margin-bottom: 16px;
  overflow: hidden;
  background: #fff;
}

.answer-detail-item .question-header {
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.question-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.question-number {
  font-weight: 600;
  color: #333;
}

.answer-status {
  display: flex;
  align-items: center;
}

.answer-detail-item .question-stem {
  padding: 20px;
  font-size: 15px;
  line-height: 1.6;
  color: #333;
  background: #fff;
}

.answer-detail-item .question-options {
  padding: 0 20px 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.answer-detail-item .option-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px;
  border-radius: 8px;
  background: #f8f9fa;
  transition: all 0.2s;
}

.answer-detail-item .option-item.selected {
  background: #e3f2fd;
  border: 1px solid #2196f3;
}

.option-label {
  font-weight: 600;
  color: #2196f3;
  min-width: 20px;
}

.option-text {
  flex: 1;
  color: #333;
  line-height: 1.4;
}

.answer-detail-item .user-answer {
  padding: 16px 20px;
  background: #f0f8ff;
  border-top: 1px solid #e5e5e5;
  color: #2196f3;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .evaluation-record-container {
    padding: 12px;
  }

  .list-header h3 {
    font-size: 16px;
  }

  .record-main {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
  }

  .record-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .summary-grid {
    grid-template-columns: 1fr;
  }

  .answer-detail-item .question-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .answer-detail-item .question-stem {
    padding: 16px;
    font-size: 14px;
  }

  .answer-detail-item .question-options {
    padding: 0 16px 12px;
  }
}
</style>
