<template>
  <div class="position-analysis">
    <h3>岗位分析</h3>
    <div v-if="student && analysisData" class="analysis-content">
      <!-- 岗位信息 -->
      <div class="position-info">
        <span class="position-label">岗位：</span>
        <span class="position-name">{{ analysisData.postName }}</span>
      </div>

      <!-- 职业能力达标情况 -->
      <div class="competency-section">
        <h4 class="section-title achieved">职业能力达标情况</h4>
        <div class="competency-grid">
          <div
            v-for="ability in analysisData.achievedAbilities"
            :key="ability.abilityId"
            class="competency-card achieved"
          >
            {{ ability.abilityName }}
          </div>
        </div>
      </div>

      <!-- 职业能力未达标情况 -->
      <div class="competency-section">
        <h4 class="section-title not-achieved">职业能力未达标情况</h4>
        <div class="competency-grid">
          <div
            v-for="ability in analysisData.notAchievedAbilities"
            :key="ability.abilityId"
            class="competency-card not-achieved"
          >
            {{ ability.abilityName }}
          </div>
        </div>
      </div>
    </div>
    <div v-else-if="student" class="analysis-empty">
      <el-empty description="暂无岗位分析数据" />
    </div>
    
    <div v-else class="no-selection">
      <el-empty description="请选择学生查看岗位分析" />
    </div>
  </div>
</template>

<script>
import { ref, watch } from 'vue'
import { listPost } from '@/api/training/post'
import { getPostModelConfig } from '@/api/training/postConfig'
import { listExamScoreByStudentId } from '@/api/student/examScore'

export default {
  name: "PositionAnalysis",
  props: {
    student: {
      type: Object,
      default: null
    }
  },
  setup(props) {
    const analysisData = ref(null)
    const loading = ref(false)
    const postList = ref([])

    // 加载岗位列表
    const loadPostList = async () => {
      try {
        const response = await listPost({})
        postList.value = response.rows || []
      } catch (error) {
        console.error('加载岗位列表失败:', error)
      }
    }

    /**
     * 计算课程的平均成绩
     */
    const calculateCourseAverage = (scores) => {
      if (!scores || scores.length === 0) return 0
      const sum = scores.reduce((total, score) => total + parseFloat(score.score || 0), 0)
      return sum / scores.length
    }

    /**
     * 计算职业能力分值
     */
    const calculateAbilityScore = (abilityConfig, studentScores) => {
      if (!abilityConfig.courseConfigs || abilityConfig.courseConfigs.length === 0) {
        return 0
      }

      let totalWeightedScore = 0
      let totalWeight = 0

      // 遍历该职业能力关联的所有课程
      abilityConfig.courseConfigs.forEach(courseConfig => {
        // 找到学生该课程的所有成绩
        const courseScores = studentScores.filter(score =>
          score.courseName === courseConfig.courseName
        )

        if (courseScores.length > 0) {
          // 计算该课程的平均成绩
          const avgScore = calculateCourseAverage(courseScores)

          // 按权重累加
          const weight = courseConfig.weight || 100
          totalWeightedScore += avgScore * (weight / 100)
          totalWeight += (weight / 100)
        }
      })

      // 返回加权平均分
      return totalWeight > 0 ? totalWeightedScore / totalWeight : 0
    }

    // 计算岗位分析数据
    const calculatePositionAnalysis = async (student) => {
      if (!student) return

      loading.value = true
      analysisData.value = null

      try {
        // 获取学生的岗位名称
        const postName = student.targetPosition || student.postName
        if (!postName) {
          analysisData.value = null
          return
        }

        // 根据岗位名称找到岗位ID
        const post = postList.value.find(p => p.postName === postName)
        if (!post) {
          analysisData.value = null
          return
        }

        // 获取岗位模型配置
        const configResponse = await getPostModelConfig(post.postId)
        const config = configResponse.data

        if (!config || !config.abilityConfigs) {
          analysisData.value = null
          return
        }

        // 获取学生的课程成绩
        const scoresResponse = await listExamScoreByStudentId(student.studentId)
        const studentScores = scoresResponse.data || []

        // 分析每个职业能力的达标情况
        const achievedAbilities = []
        const notAchievedAbilities = []

        config.abilityConfigs.forEach(abilityConfig => {
          // 计算该职业能力的分值
          const abilityScore = calculateAbilityScore(abilityConfig, studentScores)

          // 判断是否达标（计算出的分值 >= 设置的阈值）
          const threshold = abilityConfig.threshold || 60
          const isAchieved = abilityScore >= threshold

          const abilityInfo = {
            abilityId: abilityConfig.abilityId,
            abilityName: abilityConfig.abilityName,
            score: Math.round(abilityScore * 100) / 100,
            threshold: threshold,
            isAchieved: isAchieved
          }

          if (isAchieved) {
            achievedAbilities.push(abilityInfo)
          } else {
            notAchievedAbilities.push(abilityInfo)
          }
        })

        analysisData.value = {
          postName,
          achievedAbilities,
          notAchievedAbilities,
          totalCount: config.abilityConfigs.length,
          achievedCount: achievedAbilities.length
        }

      } catch (error) {
        console.error('计算岗位分析失败:', error)
        analysisData.value = null
      } finally {
        loading.value = false
      }
    }

    // 监听学生变化
    watch(() => props.student, (newStudent) => {
      if (newStudent) {
        calculatePositionAnalysis(newStudent)
      } else {
        analysisData.value = null
      }
    }, { immediate: true })

    // 初始化时加载岗位列表
    loadPostList()

    return {
      analysisData,
      loading
    }
  }
}
</script>

<style scoped>
.position-analysis {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.position-analysis h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.analysis-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.position-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.position-label {
  color: #666;
  font-size: 16px;
  font-weight: 500;
}

.position-name {
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.competency-section {
  margin-bottom: 20px;
}

.section-title {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
}

.section-title.achieved {
  color: #E67E22;
}

.section-title.not-achieved {
  color: #D32F2F;
}

.competency-grid {
  display: flex
}

.competency-card {
  padding: 12px 10px;
  border-radius: 6px;
  margin-right: 10px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid;
  max-width: 100px;
}

.competency-card.achieved {
  background-color: #E3F2FD;
  border-color: #2196F3;
  color: #1976D2;
}

.competency-card.not-achieved {
  background-color: #FFEBEE;
  border-color: #F44336;
  color: #D32F2F;
}

.analysis-loading,
.analysis-empty,
.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}


</style>
