<template>
  <div class="position-competency">
    <!-- 组件标题 -->
    <div class="component-header">
      <h3 class="component-title">岗位能力达标</h3>
      <div class="component-actions">
        <!-- 这里可以添加筛选、刷新等操作按钮 -->
      </div>
    </div>

    <!-- 图表容器 -->
    <div class="chart-container" v-loading="loading">
      <div ref="chartRef" class="echarts-chart"></div>
    </div>

    <!-- 数据为空时的占位 -->
    <div v-if="!loading && !hasData" class="empty-state">
      <el-empty description="暂无岗位能力达标数据" />
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { getPositionCompetencyStats } from '@/api/profile/groupPortrait'
import { ElMessage } from 'element-plus'

export default {
  name: "PositionCompetency",
  setup() {
    const chartRef = ref(null)
    const loading = ref(false)
    const hasData = ref(false)
    const competencyData = ref([])
    let chartInstance = null

    // 获取岗位能力达标统计数据
    const fetchPositionCompetencyData = async () => {
      try {
        loading.value = true

        // 调用后端统计接口，一次性获取所有岗位的能力达标统计数据
        const response = await getPositionCompetencyStats()

        if (response && response.data) {
          const statsData = response.data

          // 转换数据格式
          const competencyResults = statsData.map(item => ({
            postName: item.postName,
            totalStudents: item.totalStudents,
            qualifiedStudents: item.qualifiedStudents,
            competencyRate: item.competencyRate
          }))

          // 按达标率排序
          competencyResults.sort((a, b) => b.competencyRate - a.competencyRate)

          competencyData.value = competencyResults
          hasData.value = competencyResults.length > 0

          // 更新图表
          if (hasData.value) {
            updateChart()
          }
        } else {
          hasData.value = false
        }

      } catch (error) {
        console.error('获取岗位能力达标数据失败:', error)
        ElMessage.error('获取数据失败，请稍后重试')
        hasData.value = false
      } finally {
        loading.value = false
      }
    }

    // 初始化图表
    const initChart = () => {
      if (!chartRef.value) return

      if (chartInstance) {
        chartInstance.dispose()
      }

      chartInstance = echarts.init(chartRef.value)

      const option = {
        title: {
          show: false
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const data = params[0]
            const item = competencyData.value.find(item => item.postName === data.name)
            if (item) {
              return `${data.name}<br/>岗位能力达标学生: ${item.qualifiedStudents}人<br/>该岗位学生总数: ${item.totalStudents}人<br/>岗位能力达标率: ${data.value}%`
            }
            return `${data.name}<br/>岗位能力达标率: ${data.value}%`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLabel: {
            interval: 0,
            rotate: 45,
            fontSize: 12,
            formatter: function(value) {
              // 限制标签长度
              return value.length > 8 ? value.substring(0, 8) + '...' : value
            }
          }
        },
        yAxis: {
          type: 'value',
          max: 100,
          axisLabel: {
            formatter: '{value}%'
          }
        },
        series: [
          {
            name: '达标率',
            type: 'bar',
            data: [],
            barWidth: '50%',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#4A90E2' },
                { offset: 1, color: '#7BB3F0' }
              ])
            },
            label: {
              show: true,
              position: 'top',
              formatter: '{c}%',
              color: '#333',
              fontSize: 12
            }
          }
        ]
      }

      chartInstance.setOption(option)
    }

    // 更新图表数据
    const updateChart = () => {
      if (!chartInstance || !competencyData.value.length) return

      const option = {
        xAxis: {
          data: competencyData.value.map(item => item.postName)
        },
        series: [
          {
            data: competencyData.value.map(item => item.competencyRate)
          }
        ]
      }

      chartInstance.setOption(option)
    }

    // 销毁图表
    const destroyChart = () => {
      if (chartInstance) {
        chartInstance.dispose()
        chartInstance = null
      }
    }

    // 窗口大小变化处理
    const handleResize = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    // 组件挂载
    onMounted(() => {
      nextTick(() => {
        initChart()
        fetchPositionCompetencyData()
      })
      window.addEventListener('resize', handleResize)
    })

    // 组件卸载
    onUnmounted(() => {
      destroyChart()
      window.removeEventListener('resize', handleResize)
    })

    return {
      chartRef,
      loading,
      hasData
    }
  }
}
</script>

<style scoped>
.position-competency {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.component-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.component-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.component-actions {
  display: flex;
  gap: 8px;
}

.chart-container {
  flex: 1;
  min-height: 300px;
  position: relative;
}

.echarts-chart {
  width: 100%;
  height: 100%;
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
