<template>
  <div class="major-distribution">
    <!-- 组件标题 -->
    <div class="component-header">
      <h3 class="component-title">专业学生占比</h3>
      <div class="component-actions">
        <!-- 这里可以添加筛选、刷新等操作按钮 -->
      </div>
    </div>

    <!-- 图表容器 -->
    <div class="chart-container" v-loading="loading">
      <div ref="chartRef" class="echarts-chart"></div>
    </div>

    <!-- 数据为空时的占位 -->
    <div v-if="!loading && !hasData" class="empty-state">
      <el-empty description="暂无专业分布数据" />
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { listStudentInfo } from '@/api/student/student'
import { ElMessage } from 'element-plus'

export default {
  name: "MajorDistribution",
  setup() {
    const chartRef = ref(null)
    const loading = ref(false)
    const hasData = ref(false)
    const majorData = ref([])
    let chartInstance = null

    // 获取学生数据并统计专业分布
    const fetchMajorDistribution = async () => {
      try {
        loading.value = true

        // 获取所有学生数据，不分页
        const response = await listStudentInfo({
          pageNum: 1,
          pageSize: 10000 // 设置一个较大的值获取所有数据
        })

        if (response && response.rows) {
          const students = response.rows

          // 按专业统计学生数量
          const majorStats = {}

          students.forEach(student => {
            const major = student.major || '未分类'
            majorStats[major] = (majorStats[major] || 0) + 1
          })

          // 计算总人数
          const totalStudents = students.length

          // 转换为图表数据格式，按人数排序
          const distributionData = Object.keys(majorStats)
            .map(major => ({
              name: major,
              value: majorStats[major],
              percentage: totalStudents > 0 ? Math.round((majorStats[major] / totalStudents) * 100) : 0
            }))
            .sort((a, b) => b.value - a.value)

          majorData.value = distributionData
          hasData.value = distributionData.length > 0

          // 更新图表
          if (hasData.value) {
            updateChart()
          }
        } else {
          hasData.value = false
        }
      } catch (error) {
        console.error('获取专业分布数据失败:', error)
        ElMessage.error('获取数据失败，请稍后重试')
        hasData.value = false
      } finally {
        loading.value = false
      }
    }

    // 初始化图表
    const initChart = () => {
      if (!chartRef.value) return

      if (chartInstance) {
        chartInstance.dispose()
      }

      chartInstance = echarts.init(chartRef.value)

      // 定义专业对应的颜色，按照设计稿
      const colorMap = {
        '计算机科学与技术': '#E67E22',
        '机械设计制造及其自动化': '#F1C40F',
        '土木工程': '#D2B48C',
        '电气工程及其自动化': '#16A085',
        '工商管理': '#3498DB',
        '通信工程': '#2C3E50'
      }

      const option = {
        title: {
          show: false
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            return `${params.name}<br/>人数: ${params.data.originalValue}人<br/>占比: ${params.percent}%`
          }
        },
        legend: {
          orient: 'horizontal',
          bottom: '0',
          left: 'center',
          itemGap: 20,
          itemWidth: 14,
          itemHeight: 14,
          textStyle: {
            fontSize: 12,
            color: '#666'
          },
          formatter: function(name) {
            // 显示专业名称，如果太长则截断
            return name.length > 10 ? name.substring(0, 10) + '...' : name
          }
        },
        series: [
          {
            name: '专业分布',
            type: 'pie',
            radius: ['45%', '75%'],
            center: ['50%', '45%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 0,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'outside',
              formatter: function(params) {
                return `${params.name}\n${params.data.originalValue}`
              },
              fontSize: 12,
              color: '#666',
              lineHeight: 16
            },
            labelLine: {
              show: true,
              length: 15,
              length2: 10,
              smooth: false,
              lineStyle: {
                color: '#999'
              }
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            data: []
          }
        ]
      }

      chartInstance.setOption(option)
    }

    // 更新图表数据
    const updateChart = () => {
      if (!chartInstance || !majorData.value.length) return

      // 定义专业对应的颜色
      const colorMap = {
        '计算机科学与技术': '#E67E22',
        '机械设计制造及其自动化': '#F1C40F',
        '土木工程': '#D2B48C',
        '电气工程及其自动化': '#16A085',
        '工商管理': '#3498DB',
        '通信工程': '#2C3E50'
      }

      // 默认颜色数组，用于未匹配的专业
      const defaultColors = ['#E74C3C', '#9B59B6', '#34495E', '#95A5A6', '#F39C12']

      const chartData = majorData.value.map((item, index) => ({
        name: item.name,
        value: item.value,
        originalValue: item.value,
        itemStyle: {
          color: colorMap[item.name] || defaultColors[index % defaultColors.length]
        }
      }))

      const option = {
        series: [
          {
            data: chartData
          }
        ]
      }

      chartInstance.setOption(option)
    }

    // 销毁图表
    const destroyChart = () => {
      if (chartInstance) {
        chartInstance.dispose()
        chartInstance = null
      }
    }

    // 窗口大小变化处理
    const handleResize = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    // 组件挂载
    onMounted(() => {
      nextTick(() => {
        initChart()
        fetchMajorDistribution()
      })
      window.addEventListener('resize', handleResize)
    })

    // 组件卸载
    onUnmounted(() => {
      destroyChart()
      window.removeEventListener('resize', handleResize)
    })

    return {
      chartRef,
      loading,
      hasData
    }
  }
}
</script>

<style scoped>
.major-distribution {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.component-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.component-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.component-actions {
  display: flex;
  gap: 8px;
}

.chart-container {
  flex: 1;
  min-height: 300px;
  position: relative;
}

.echarts-chart {
  width: 100%;
  height: 100%;
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
