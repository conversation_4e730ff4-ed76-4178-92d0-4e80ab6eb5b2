<template>
  <div class="position-overview">
    <!-- 组件标题 -->
    <div class="component-header">
      <h3 class="component-title">岗位标签</h3>
      <div class="component-actions">
        <!-- 这里可以添加筛选、刷新等操作按钮 -->
      </div>
    </div>

    <!-- 图表容器 -->
    <div class="chart-container" v-loading="loading">
      <div ref="chartRef" class="echarts-chart"></div>
    </div>

    <!-- 数据为空时的占位 -->
    <div v-if="!loading && !hasData" class="empty-state">
      <el-empty description="暂无岗位标签数据" />
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import 'echarts-wordcloud'
import { listStudentInfo } from '@/api/student/student'
import { ElMessage } from 'element-plus'

export default {
  name: "PositionOverview",
  setup() {
    const chartRef = ref(null)
    const loading = ref(false)
    const hasData = ref(false)
    const positionData = ref([])
    let chartInstance = null

    // 获取学生数据并统计目标岗位
    const fetchPositionData = async () => {
      try {
        loading.value = true

        // 获取所有学生数据，不分页
        const response = await listStudentInfo({
          pageNum: 1,
          pageSize: 10000 // 设置一个较大的值获取所有数据
        })

        if (response && response.rows) {
          const students = response.rows

          // 统计目标岗位
          const positionStats = {}

          students.forEach(student => {
            // 优先使用合同中的岗位名称，其次使用目标岗位
            const position = student.postName || student.targetPosition

            if (position && position.trim()) {
              const positionName = position.trim()
              positionStats[positionName] = (positionStats[positionName] || 0) + 1
            }
          })

          // 转换为词云数据格式，按数量排序
          const wordCloudData = Object.keys(positionStats)
            .map(position => ({
              name: position,
              value: positionStats[position]
            }))
            .sort((a, b) => b.value - a.value)

          positionData.value = wordCloudData
          hasData.value = wordCloudData.length > 0

          // 更新图表
          if (hasData.value) {
            updateChart()
          }
        } else {
          hasData.value = false
        }
      } catch (error) {
        console.error('获取岗位标签数据失败:', error)
        ElMessage.error('获取数据失败，请稍后重试')
        hasData.value = false
      } finally {
        loading.value = false
      }
    }

    // 初始化图表
    const initChart = () => {
      if (!chartRef.value) return

      if (chartInstance) {
        chartInstance.dispose()
      }

      chartInstance = echarts.init(chartRef.value)

      const option = {
        title: {
          show: false
        },
        tooltip: {
          show: true,
          formatter: function(params) {
            return `${params.name}<br/>学生数量: ${params.value}人`
          }
        },
        series: [{
          type: 'wordCloud',
          gridSize: 8,
          sizeRange: [14, 60],
          rotationRange: [-45, 45],
          rotationStep: 45,
          shape: 'pentagon',
          width: '100%',
          height: '100%',
          drawOutOfBound: false,
          layoutAnimation: true,
          textStyle: {
            fontFamily: 'sans-serif',
            fontWeight: 'bold',
            color: function () {
              // 预定义颜色数组，确保颜色搭配协调
              const colors = [
                '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
                '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F',
                '#FF8A80', '#82B1FF', '#B39DDB', '#A5D6A7'
              ];
              return colors[Math.floor(Math.random() * colors.length)];
            }
          },
          emphasis: {
            focus: 'self',
            textStyle: {
              shadowBlur: 10,
              shadowColor: '#333'
            }
          },
          data: []
        }]
      }

      chartInstance.setOption(option)
    }

    // 更新图表数据
    const updateChart = () => {
      if (!chartInstance || !positionData.value.length) return

      const option = {
        series: [{
          data: positionData.value
        }]
      }

      chartInstance.setOption(option)
    }

    // 销毁图表
    const destroyChart = () => {
      if (chartInstance) {
        chartInstance.dispose()
        chartInstance = null
      }
    }

    // 窗口大小变化处理
    const handleResize = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    // 组件挂载
    onMounted(() => {
      nextTick(() => {
        initChart()
        fetchPositionData()
      })
      window.addEventListener('resize', handleResize)
    })

    // 组件卸载
    onUnmounted(() => {
      destroyChart()
      window.removeEventListener('resize', handleResize)
    })

    return {
      chartRef,
      loading,
      hasData
    }
  }
}
</script>

<style scoped>
.position-overview {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.component-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.component-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.component-actions {
  display: flex;
  gap: 8px;
}

.chart-container {
  flex: 1;
  min-height: 300px;
  position: relative;
}

.echarts-chart {
  width: 100%;
  height: 100%;
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
