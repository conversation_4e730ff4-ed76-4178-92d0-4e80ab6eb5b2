<template>
  <div class="major-success-ranking">
    <!-- 组件标题 -->
    <div class="component-header">
      <h3 class="component-title">专业学生面试成功排名</h3>
      <div class="component-actions">
        <!-- 这里可以添加筛选等操作按钮 -->
      </div>
    </div>

    <!-- 图表容器 -->
    <div class="chart-container" v-loading="loading">
      <div ref="chartRef" class="echarts-chart"></div>
    </div>

    <!-- 数据为空时的占位 -->
    <div v-if="!loading && !hasData" class="empty-state">
      <el-empty description="暂无专业成功率数据" />
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { listStudentInfo } from '@/api/student/student'
import { ElMessage } from 'element-plus'

export default {
  name: "MajorSuccessRanking",
  setup() {
    const chartRef = ref(null)
    const loading = ref(false)
    const hasData = ref(false)
    const rankingData = ref([])
    let chartInstance = null

    // 获取学生数据并计算专业面试成功排名
    const fetchMajorSuccessRanking = async () => {
      try {
        loading.value = true

        // 获取所有学生数据，不分页
        const response = await listStudentInfo({
          pageNum: 1,
          pageSize: 10000 // 设置一个较大的值获取所有数据
        })

        if (response && response.rows) {
          const students = response.rows

          // 按专业分组统计
          const majorStats = {}

          students.forEach(student => {
            const major = student.major || '未分类'

            if (!majorStats[major]) {
              majorStats[major] = {
                total: 0,
                success: 0
              }
            }

            majorStats[major].total++

            // 学生状态为 "2" 表示成功签约
            if (student.studentStatus === '2') {
              majorStats[major].success++
            }
          })

          // 计算成功签约数量并排序
          const rankingList = Object.keys(majorStats).map(major => ({
            major,
            successCount: majorStats[major].success,
            totalCount: majorStats[major].total,
            successRate: majorStats[major].total > 0
              ? Math.round((majorStats[major].success / majorStats[major].total) * 100)
              : 0
          }))

          // 按成功签约数量从多到少排序
          rankingList.sort((a, b) => b.successCount - a.successCount)

          rankingData.value = rankingList
          hasData.value = rankingList.length > 0

          // 更新图表
          if (hasData.value) {
            updateChart()
          }
        } else {
          hasData.value = false
        }
      } catch (error) {
        console.error('获取专业面试成功排名数据失败:', error)
        ElMessage.error('获取数据失败，请稍后重试')
        hasData.value = false
      } finally {
        loading.value = false
      }
    }

    // 初始化图表
    const initChart = () => {
      if (!chartRef.value) return

      if (chartInstance) {
        chartInstance.dispose()
      }

      chartInstance = echarts.init(chartRef.value)

      // 初始化空图表
      const option = {
        title: {
          show: false
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const data = params[0]
            const item = rankingData.value.find(item => item.major === data.name)
            if (item) {
              return `${data.name}<br/>成功签约: ${item.successCount}人<br/>总人数: ${item.totalCount}人<br/>成功率: ${item.successRate}%`
            }
            return `${data.name}<br/>成功签约: ${data.value}人`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}人'
          }
        },
        yAxis: {
          type: 'category',
          data: [],
          axisLabel: {
            interval: 0
          }
        },
        series: [
          {
            name: '成功签约人数',
            type: 'bar',
            data: [],
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: '#FFA726' },
                { offset: 1, color: '#FF7043' }
              ])
            },
            barWidth: '60%',
            label: {
              show: true,
              position: 'right',
              formatter: '{c}人',
              color: '#333'
            }
          }
        ]
      }

      chartInstance.setOption(option)
    }

    // 更新图表数据
    const updateChart = () => {
      if (!chartInstance || !rankingData.value.length) return

      const option = {
        yAxis: {
          data: rankingData.value.map(item => item.major)
        },
        series: [
          {
            data: rankingData.value.map(item => item.successCount)
          }
        ]
      }

      chartInstance.setOption(option)
    }

    // 销毁图表
    const destroyChart = () => {
      if (chartInstance) {
        chartInstance.dispose()
        chartInstance = null
      }
    }

    // 窗口大小变化处理
    const handleResize = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    // 组件挂载
    onMounted(() => {
      nextTick(() => {
        initChart()
        fetchMajorSuccessRanking()
      })
      window.addEventListener('resize', handleResize)
    })

    // 组件卸载
    onUnmounted(() => {
      destroyChart()
      window.removeEventListener('resize', handleResize)
    })

    return {
      chartRef,
      loading,
      hasData
    }
  }
}
</script>

<style scoped>
.major-success-ranking {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.component-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.component-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.component-actions {
  display: flex;
  gap: 8px;
}

.chart-container {
  flex: 1;
  min-height: 300px;
  position: relative;
}

.echarts-chart {
  width: 100%;
  height: 100%;
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
