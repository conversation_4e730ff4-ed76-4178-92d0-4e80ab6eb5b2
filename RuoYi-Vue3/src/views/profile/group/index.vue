<template>
  <div class="app-container">
    <div class="group-portrait-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h2 class="page-title">学生群体画像</h2>
      </div>

      <!-- 图表网格布局 -->
      <div class="charts-grid">
        <!-- 左上：专业学生面试成功排名 -->
        <div class="chart-card">
          <MajorSuccessRanking />
        </div>

        <!-- 右上：岗位标签 -->
        <div class="chart-card">
          <PositionOverview />
        </div>

        <!-- 左下：专业学生占比 -->
        <div class="chart-card">
          <MajorDistribution />
        </div>

        <!-- 右下：岗位能力达标 -->
        <div class="chart-card">
          <PositionCompetency />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import MajorSuccessRanking from './components/MajorSuccessRanking.vue'
import PositionOverview from './components/PositionOverview.vue'
import MajorDistribution from './components/MajorDistribution.vue'
import PositionCompetency from './components/PositionCompetency.vue'

export default {
  name: "GroupPortrait",
  components: {
    MajorSuccessRanking,
    PositionOverview,
    MajorDistribution,
    PositionCompetency
  },
  setup() {
    // 响应式数据
    const loading = ref(false)

    // 组件挂载时的初始化
    onMounted(() => {
      // 这里可以添加页面级别的数据加载逻辑
      console.log('学生群体画像页面已加载')
    })

    return {
      loading
    }
  }
}
</script>

<style scoped>
.group-portrait-container {
  padding: 20px;
  min-height: calc(100vh - 120px);
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 20px;
  height: calc(100vh - 200px);
  min-height: 600px;
}

.chart-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(4, 1fr);
    height: auto;
    min-height: auto;
  }

  .chart-card {
    min-height: 400px;
  }
}

@media (max-width: 768px) {
  .group-portrait-container {
    padding: 16px;
  }

  .page-title {
    font-size: 20px;
  }

  .chart-card {
    padding: 16px;
    min-height: 350px;
  }
}
</style>