<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="机构名称" prop="structureName">
        <el-input
          v-model="queryParams.structureName"
          placeholder="请输入机构名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="机构类型" prop="structureType">
        <el-select
          v-model="queryParams.structureType"
          placeholder="请选择机构类型"
          style="width: 240px"
          clearable
        >
          <el-option
            v-for="dict in organization_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['organization:structure:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['organization:structure:remove']"
          >批量删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Sort" @click="toggleExpandAll"
          >展开/折叠</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="structureList"
      row-key="structureId"
      border
      :default-expand-all="isExpandAll"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="80" align="center" />
      <el-table-column label="机构名称" prop="structureName" />
      <el-table-column label="机构类型" align="center" prop="structureType">
        <template #default="scope">
          <dict-tag
            :options="organization_type"
            :value="scope.row.structureType"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="机构简称"
        align="center"
        prop="structureAbbreviation"
      />
      <el-table-column label="负责人" align="center" prop="director" />
      <el-table-column label="联系方式" align="center" prop="contactInfo" />
      <el-table-column label="简介" align="center" prop="description" />
      <el-table-column
        label="操作"
        align="left"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleDetail(scope.row)"
            v-hasPermi="['structure:structure:edit']"
            >查看</el-button
          >
          <el-button
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['structure:structure:edit']"
            >编辑</el-button
          >
          <el-button
            link
            type="primary"
            @click="handleAdd(scope.row)"
            v-hasPermi="['structure:structure:add']"
            >新增子项</el-button
          >
          <el-button
            link
            type="primary"
            v-if="scope.row.parentId != 0"
            @click="handleDelete(scope.row)"
            v-hasPermi="['structure:structure:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改机构信息对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form
        ref="structureRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item
              label="机构名称"
              prop="structureName"
              label-position="top"
            >
              <el-input
                v-model="form.structureName"
                placeholder="请输入机构名称"
              /> </el-form-item></el-col
        ></el-row>
        <el-row>
          <el-col :span="12"
            ><el-form-item
              label="上级机构"
              prop="parentId"
              label-position="top"
            >
              <el-tree-select
                v-model="form.parentId"
                :data="structureOptions"
                :props="{
                  value: 'structureId',
                  label: 'structureName',
                  children: 'children',
                }"
                value-key="structureId"
                placeholder="请选择上级机构ID"
                check-strictly
              /> </el-form-item></el-col
        ></el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="机构类型"
              prop="structureType"
              label-position="top"
            >
              <el-select
                v-model="form.structureType"
                placeholder="请选择机构类型"
              >
                <el-option
                  v-for="dict in organization_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select> </el-form-item></el-col
        ></el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="机构简称"
              prop="structureAbbreviation"
              label-position="top"
            >
              <el-input
                v-model="form.structureAbbreviation"
                placeholder="请输入机构简称"
              /> </el-form-item></el-col
        ></el-row>
        <el-row>
          <el-col :span="12"
            ><el-form-item label="负责人" prop="director" label-position="top">
              <el-input
                v-model="form.director"
                placeholder="请输入负责人"
              /> </el-form-item></el-col
        ></el-row>
        <el-row>
          <el-col :span="12"
            ><el-form-item
              label="联系方式"
              prop="contactInfo"
              label-position="top"
            >
              <el-input
                v-model="form.contactInfo"
                placeholder="请输入联系方式"
              /> </el-form-item></el-col
        ></el-row>
        <el-row>
          <el-col :span="24"
            ><el-form-item label="简介" prop="description" label-position="top">
              <el-input
                v-model="form.description"
                type="textarea"
                rows="5"
                placeholder="请输入简介"
                show-word-limit
                :maxlength="300"
              /> </el-form-item
          ></el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="title" v-model="detail" width="600px" append-to-body>
      <el-descriptions class="margin-top" :column="2" direction="vertical">
        <el-descriptions-item label="机构名称">{{
          form.structureName
        }}</el-descriptions-item>
        <el-descriptions-item label="上级机构">{{
          getStructureNameInTree(form.parentId, structureOptions)
        }}</el-descriptions-item>
        <el-descriptions-item label="机构类型">{{
          form.structureType || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="机构简称">{{
          form.structureAbbreviation || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="负责人">{{
          form.director || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="联系方式">{{
          form.contactInfo || "-"
        }}</el-descriptions-item>

        <el-descriptions-item label="简介">{{
          form.description || "-"
        }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detail = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Structure">
import {
  listStructure,
  getStructure,
  delStructure,
  addStructure,
  updateStructure,
} from "@/api/organization/structure";

const { proxy } = getCurrentInstance();
const { organization_type } = proxy.useDict("organization_type");

const structureList = ref([]);
const structureOptions = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const isExpandAll = ref(true);
const refreshTable = ref(true);
const ids = ref([]);
const multiple = ref(true);
const single = ref(true);
const detail = ref(false);

const data = reactive({
  form: {},
  queryParams: {
    structureName: null,
    structureType: null,
  },
  rules: {
    structureName: [
      { required: true, message: "请输入机构名称！", trigger: "blur" },
    ],
    parentId: [
      { required: true, message: "请选择上级机构！", trigger: "change" },
    ],
    structureType: [
      { required: true, message: "请选择机构类型！", trigger: "change" },
    ],
    contactInfo: [
      { required: true, message: "请输入联系方式！", trigger: "blur" },
      {
        pattern: /^\d{11}$/,
        message: "手机号必须为11位数字",
        trigger: "blur",
      },
    ],
  },
});

// 递归查找树形结构中的机构名称
const getStructureNameInTree = (id, sTree = []) => {
  console.log(sTree);
  for (const s of sTree) {
    if (s.structureId === id) {
      return s.structureName;
    }
    // 如果有子机构，递归查找
    if (s.children && s.children.length) {
      const result = getStructureNameInTree(id, s.children);
      if (result) return result;
    }
  }
  return "未知机构";
};

const { queryParams, form, rules } = toRefs(data);

/** 查询机构信息列表 */
function getList() {
  console.log(organization_type);
  loading.value = true;
  listStructure(queryParams.value).then((response) => {
    structureList.value = proxy.handleTree(
      response.data,
      "structureId",
      "parentId"
    );
    loading.value = false;
  });
}

/** 查询机构信息下拉树结构 */
function getTreeselect() {
  listStructure().then((response) => {
    structureOptions.value = [];
    const data = { structureId: 0, structureName: "无", children: [] };
    data.children = proxy.handleTree(response.data, "structureId", "parentId");
    structureOptions.value.push(data);
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    structureId: null,
    structureName: null,
    parentId: null,
    structureType: null,
    structureAbbreviation: null,
    director: null,
    contactInfo: null,
    description: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
  };
  proxy.resetForm("structureRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.structureId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}
/** 新增按钮操作 */
function handleAdd(row) {
  reset();
  getTreeselect();
  if (row != null && row.structureId) {
    form.value.parentId = row.structureId;
  } else {
    form.value.parentId = 0;
  }
  open.value = true;
  title.value = "添加机构信息";
}

/** 展开/折叠操作 */
function toggleExpandAll() {
  refreshTable.value = false;
  isExpandAll.value = !isExpandAll.value;
  nextTick(() => {
    refreshTable.value = true;
  });
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  reset();
  await getTreeselect();
  if (row != null) {
    form.value.parentId = row.parentId;
  }
  getStructure(row.structureId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改机构信息";
  });
}
/** 详情按钮操作 */
async function handleDetail(row) {
  reset();
  await getTreeselect();
  if (row != null) {
    form.value.parentId = row.parentId;
  }
  getStructure(row.structureId).then((response) => {
    form.value = response.data;
    detail.value = true;
    title.value = "详情";
  });
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["structureRef"].validate((valid) => {
    if (valid) {
      if (form.value.structureId != null) {
        updateStructure(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addStructure(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _structureIds = row.structureId || ids.value;
  const tipMsg =
    ids.value.length == 0
      ? `是否确认删除"${_structureIds}"?`
      : "是否确认删除所选数据？";
  proxy.$modal
    .confirm(tipMsg)
    .then(function () {
      return delStructure(_structureIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

getList();
</script>
