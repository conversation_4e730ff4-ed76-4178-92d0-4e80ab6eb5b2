<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="专业名称" prop="majorName">
        <el-input
          v-model="queryParams.majorName"
          placeholder="请输入专业名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['organization:major:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['organization:major:import']"
          >导入</el-button
        >
      </el-col>       
       <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['organization:major:remove']"
            >批量删除</el-button
          >
        </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      border
      :data="majorList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="80" align="center" />
      <el-table-column
        label="专业名称"
        width="200"
        align="center"
        prop="majorName"
      />
      <el-table-column label="专业描述" align="center" prop="majorDesc" />
      <el-table-column
        width="200"
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleDetail(scope.row)"
            v-hasPermi="['organization:major:detail']"
            >查看</el-button
          >
          <el-button
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['organization:major:edit']"
            >编辑</el-button
          >
          <el-button
            link
            type="primary"
            @click="handleDelete(scope.row)"
            v-hasPermi="['organization:major:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改专业对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="majorRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="专业名称" prop="majorName" label-position="top">
          <el-input
            v-model="form.majorName"
            placeholder="请输入专业名称"
            type="text"
            maxlength="30"
            show-word-limit
            clearable
          />
        </el-form-item>
        <el-form-item label="专业介绍" prop="majorDesc" label-position="top">
          <el-input
            v-model="form.majorDesc"
            type="textarea"
            :rows="10"
            placeholder="请输入专业介绍"
            maxlength="600"
            show-word-limit="true"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 详情 -->
    <el-dialog :title="title" v-model="detail" width="600px" append-to-body>
      <el-descriptions class="margin-top" :column="1" direction="vertical">
        <el-descriptions-item label="专业名称">{{
          form.majorName
        }}</el-descriptions-item>
        <el-descriptions-item label="专业介绍">{{
          form.majorDesc || "-"
        }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detail = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 专业导入对话框 -->
    <ImportDialog
      v-model="importDialogVisible"
      :title="importTitle"
      :import-url="importUrl"
      :template-url="templateUrl"
      @success="handleImportSuccess"
      @error="handleImportError"
      @download-template="handleDownloadTemplate"
    />
  </div>
</template>

<script setup name="Major">
import {
  listMajor,
  getMajor,
  delMajor,
  addMajor,
  updateMajor,
} from "@/api/organization/major";

import ImportDialog from "@/components/ImportDialog/index.vue";
import { useImport, createImportConfig } from "@/composables/useImport";

const { proxy } = getCurrentInstance();

const majorList = ref([]);
const open = ref(false);
const detail = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    majorName: null,
  },
  rules: {
    majorName: [
      { required: true, message: "请输入专业名称！", trigger: "blur" },
    ],
  },
});
const { queryParams, form, rules } = toRefs(data);

/** 查询专业列表 */
function getList() {
  loading.value = true;
  listMajor(queryParams.value).then((response) => {
    majorList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    majorId: null,
    majorName: null,
    majorDesc: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
  };
  proxy.resetForm("majorRef");
}
// 使用导入组合式函数
const importConfig = createImportConfig(
  "/organization/major",
  "专业",
  getList,
  proxy.download
);

const {
  importDialogVisible,
  importTitle,
  openImportDialog,
  handleImportSuccess,
  handleImportError,
  handleDownloadTemplate,
  importUrl,
  templateUrl,
} = useImport(importConfig);

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.majorId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "新建";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _majorId = row.majorId || ids.value;
  getMajor(_majorId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改";
  });
}
/** 详情按钮操作 */
function handleDetail(row) {
  reset();
  const _majorId = row.majorId || ids.value;
  getMajor(_majorId).then((response) => {
    form.value = response.data;
    detail.value = true;
    title.value = "详情";
  });
}
/** 导入按钮操作 */
function handleImport() {
  openImportDialog();
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["majorRef"].validate((valid) => {
    if (valid) {
      if (form.value.majorId != null) {
        updateMajor(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addMajor(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _majorIds = row.majorId || ids.value;
  const tipMsg =
    ids.value.length == 0
      ? `是否确认删除"${_majorIds}"?`
      : "是否确认删除所选数据？";
  proxy.$modal
    .confirm(tipMsg)
    .then(function () {
      return delMajor(_majorIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "organization/major/export",
    {
      ...queryParams.value,
    },
    `major_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
