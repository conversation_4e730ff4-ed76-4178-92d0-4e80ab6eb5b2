<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="人员姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入人员姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['organization:user:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['organization:user:import']"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['organization:user:remove']"
          >批量删除</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      border
      :data="userList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="80" align="center" />
      <el-table-column
        label="人员姓名"
        width="120"
        align="center"
        prop="userName"
      />
      <el-table-column label="所属机构" align="left" prop="orgName" />
      <el-table-column
        label="职务"
        width="120"
        align="center"
        prop="position"
      />
      <el-table-column label="性别" width="80" align="center" prop="gender">
        <template #default="scope">
          <dict-tag :options="user_sex" :value="scope.row.gender" />
        </template>
      </el-table-column>
      <el-table-column
        label="联系方式"
        width="120"
        align="center"
        prop="contactInfo"
      />
      <el-table-column
        label="在职状态"
        width="80"
        align="center"
        prop="workStatus"
      >
        <template #default="scope">
          <dict-tag :options="work_status" :value="scope.row.workStatus" />
        </template>
      </el-table-column>
      <el-table-column
        width="180"
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleDetail(scope.row)"
            v-hasPermi="['organization:user:detail']"
            >详情</el-button
          >
          <el-button
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['organization:user:edit']"
            >编辑</el-button
          >
          <el-button
            link
            type="primary"
            @click="handleDelete(scope.row)"
            v-hasPermi="['organization:user:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改机构人员对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="userRef" :model="form" :rules="rules" label-width="80px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="人员姓名" prop="userName" label-position="top">
              <el-input v-model="form.userName" placeholder="请输入人员姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属机构" prop="orgName" label-position="top">
              <el-tree-select
                v-model="form.orgName"
                :data="structureOptions"
                :props="{
                  value: 'structureName',
                  label: 'structureName',
                  children: 'children',
                }"
                value-key="structureId"
                placeholder="请选择所属机构"
                check-strictly
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="职务" prop="position" label-position="top">
              <el-input v-model="form.position" placeholder="请输入职务" />
            </el-form-item>
          </el-col>
          <el-col :span="12"
            ><el-form-item
              label="联系方式"
              prop="contactInfo"
              label-position="top"
            >
              <el-input
                v-model="form.contactInfo"
                placeholder="请输入联系方式"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="性别" prop="gender" label-position="top">
          <el-radio-group v-model="form.gender">
            <el-radio
              v-for="dict in user_sex"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="在职状态" prop="workStatus" label-position="top">
          <el-radio-group v-model="form.workStatus">
            <el-radio
              v-for="dict in work_status"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="title" v-model="detail" width="600px" append-to-body>
      <el-descriptions class="margin-top" :column="2" direction="vertical">
        <el-descriptions-item label="人员姓名">{{
          form.userName
        }}</el-descriptions-item>
        <el-descriptions-item label="所属机构">{{
          form.orgName || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="职务">{{
          form.position || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="联系方式">{{
          form.contactInfo || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="性别">{{
          form.gender || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="在职状态">{{
          form.workStatus || "-"
        }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detail = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 导入对话框 -->
    <ImportDialog
      v-model="importDialogVisible"
      :title="importTitle"
      :import-url="importUrl"
      :template-url="templateUrl"
      @success="handleImportSuccess"
      @error="handleImportError"
      @download-template="handleDownloadTemplate"
    />
  </div>
</template>

<script setup name="User">
import {
  listUser,
  getUser,
  delUser,
  addUser,
  updateUser,
} from "@/api/organization/user";
import { listStructure } from "@/api/organization/structure";
import ImportDialog from "@/components/ImportDialog/index.vue";
import { useImport, createImportConfig } from "@/composables/useImport";
const { proxy } = getCurrentInstance();
const { user_sex, work_status } = proxy.useDict("user_sex", "work_status");

const userList = ref([]);
const open = ref(false);
const detail = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const structureOptions = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: null,
  },
  rules: {
    userName: [
      { required: true, message: "请输入人员姓名！", trigger: "blur" },
    ],
    orgName: [
      { required: true, message: "请选择所属机构！", trigger: "change" },
    ],
    contactInfo: [
      { required: true, message: "请输入联系方式！", trigger: "blur" },
      {
        pattern: /^\d{11}$/,
        message: "手机号必须为11位数字",
        trigger: "blur",
      },
    ],
    workStatus: [
      { required: true, message: "请选择在职状态！", trigger: "change" },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

// 使用导入组合式函数
const importConfig = createImportConfig(
  "/organization/user",
  "机构人员",
  getList,
  proxy.download
);

const {
  importDialogVisible,
  importTitle,
  openImportDialog,
  handleImportSuccess,
  handleImportError,
  handleDownloadTemplate,
  importUrl,
  templateUrl,
} = useImport(importConfig);

/** 查询机构人员列表 */
function getList() {
  loading.value = true;
  listUser(queryParams.value).then((response) => {
    userList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    userId: null,
    userName: null,
    orgName: null,
    position: null,
    contactInfo: null,
    gender: "男",
    workStatus: "在职",
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
  };
  proxy.resetForm("userRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.userId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  getTreeselect();
  open.value = true;
  title.value = "添加机构人员";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _userId = row.userId || ids.value;
  getUser(_userId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改机构人员";
  });
}
/** 详情按钮操作 */
async function handleDetail(row) {
  reset();
  const _userId = row.userId || ids.value;
  getUser(_userId).then((response) => {
    form.value = response.data;
    detail.value = true;
    title.value = "详细";
  });
}
/** 导入按钮操作 */
function handleImport() {
  openImportDialog();
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["userRef"].validate((valid) => {
    if (valid) {
      if (form.value.userId != null) {
        updateUser(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addUser(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _userIds = row.userId || ids.value;
  const tipMsg =
    ids.value.length == 0
      ? `是否确认删除"${row.userName}"?`
      : "是否确认删除所选数据？";
  proxy.$modal
    .confirm(tipMsg)
    .then(function () {
      return delUser(_userIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "user/user/export",
    {
      ...queryParams.value,
    },
    `user_${new Date().getTime()}.xlsx`
  );
}

/** 查询机构信息下拉树结构 */
function getTreeselect() {
  listStructure().then((response) => {
    structureOptions.value = [];
    const data = { structureId: 0, structureName: "无", children: [] };
    data.children = proxy.handleTree(response.data, "structureId", "parentId");
    structureOptions.value.push(data);
  });
}
getList();
</script>
