<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="企业名称" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输入企业名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['organization:company:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['organization:company:import']"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['organization:company:remove']"
          >批量删除</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      border
      :data="companyList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="80" align="center" />
      <el-table-column
        label="企业名称"
        width="200"
        align="left"
        prop="companyName"
      />
      <el-table-column
        label="统一社会信用代码"
        align="center"
        width="150"
        prop="socialCode"
      />
      <el-table-column
        label="提供岗位数"
        width="120"
        align="center"
        prop="jobCount"
      />
      <el-table-column
        label="签约学生数"
        width="120"
        align="center"
        prop="signedCount"
      />
      <el-table-column
        label="法人"
        width="120"
        align="center"
        prop="legalPerson"
      />
      <el-table-column label="所在地址" align="left" prop="address" />
      <el-table-column
        label="联系人"
        width="120"
        align="center"
        prop="contactPerson"
      />
      <el-table-column
        label="联系方式"
        width="120"
        align="center"
        prop="contactInfo"
      />
      <el-table-column
        width="180"
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleDetail(scope.row)"
            v-hasPermi="['organization:company:detail']"
            >查看</el-button
          >
          <el-button
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['organization:company:edit']"
            >编辑</el-button
          >
          <el-button
            link
            type="primary"
            @click="handleDelete(scope.row)"
            v-hasPermi="['organization:company:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公司对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="companyRef" :model="form" :rules="rules">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="公司名称"
              prop="companyName"
              label-position="top"
            >
              <el-input
                v-model="form.companyName"
                placeholder="请输入公司名称"
                show-word-limit
                maxlength="30"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="统一社会信用代码"
              prop="socialCode"
              label-position="top"
            >
              <el-input
                v-model="form.socialCode"
                placeholder="请输入统一社会信用代码"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="法人" prop="legalPerson" label-position="top">
              <el-input
                v-model="form.legalPerson"
                placeholder="请输入法人"
              /> </el-form-item
          ></el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="所在地址" prop="address" label-position="top">
              <el-input v-model="form.address" placeholder="请输入所在地址" />
            </el-form-item> </el-col
        ></el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="联系人"
              prop="contactPerson"
              label-position="top"
            >
              <el-input
                v-model="form.contactPerson"
                placeholder="请输入联系人"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="联系方式"
              prop="contactInfo"
              label-position="top"
            >
              <el-input
                v-model="form.contactInfo"
                placeholder="请输入联系方式"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 详情 -->
    <el-dialog :title="title" v-model="detail" width="600px" append-to-body>
      <el-descriptions class="margin-top" :column="2" direction="vertical">
        <el-descriptions-item label="企业名称" :span="2">{{
          form.companyName
        }}</el-descriptions-item>
      </el-descriptions>
      <el-tabs v-model="activeName" @tab-click="handleTab">
        <el-tab-pane label="基本信息" name="base">
          <el-descriptions class="margin-top" :column="2" direction="vertical">
            <el-descriptions-item label="统一社会信用代码">{{
              form.socialCode || "-"
            }}</el-descriptions-item>
            <el-descriptions-item label="法人">{{
              form.legalPerson || "-"
            }}</el-descriptions-item>
            <el-descriptions-item label="所在地址" :span="2">{{
              form.address || "-"
            }}</el-descriptions-item>
            <el-descriptions-item label="联系人">{{
              form.contactPerson || "-"
            }}</el-descriptions-item>
            <el-descriptions-item label="联系方式">{{
              form.contactInfo || "-"
            }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="岗位信息" name="job">岗位信息</el-tab-pane>
      </el-tabs>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detail = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 企业导入对话框 -->
    <ImportDialog
      v-model="importDialogVisible"
      :title="importTitle"
      :import-url="importUrl"
      :template-url="templateUrl"
      @success="handleImportSuccess"
      @error="handleImportError"
      @download-template="handleDownloadTemplate"
    />
  </div>
</template>

<script setup name="Company">
import {
  listCompany,
  getCompany,
  delCompany,
  addCompany,
  updateCompany,
} from "@/api/organization/company";

import ImportDialog from "@/components/ImportDialog/index.vue";
import { useImport, createImportConfig } from "@/composables/useImport";

const { proxy } = getCurrentInstance();

const companyList = ref([]);
const open = ref(false);
const detail = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const activeName = ref("base");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    companyName: null,
  },
  rules: {
    companyName: [
      { required: true, message: "请输入公司名称！", trigger: "blur" },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公司列表 */
function getList() {
  loading.value = true;
  listCompany(queryParams.value).then((response) => {
    companyList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    companyId: null,
    companyName: null,
    socialCode: null,
    legalPerson: null,
    address: null,
    contactPerson: null,
    contactInfo: null,
    jobCount: null,
    signedCount: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
  };
  proxy.resetForm("companyRef");
}
// 使用导入组合式函数
const importConfig = createImportConfig(
  "/organization/company",
  "企业",
  getList,
  proxy.download
);

const {
  importDialogVisible,
  importTitle,
  openImportDialog,
  handleImportSuccess,
  handleImportError,
  handleDownloadTemplate,
  importUrl,
  templateUrl,
} = useImport(importConfig);

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.companyId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}
/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "新建";
}

/** 详情按钮操作 */
function handleDetail(row) {
  reset();
  const _companyId = row.companyId || ids.value;
  getCompany(_companyId).then((response) => {
    form.value = response.data;
    detail.value = true;
    title.value = "详情";
  });
}
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _companyId = row.companyId || ids.value;
  getCompany(_companyId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改";
  });
}
/** 导入按钮操作 */
function handleImport() {
  openImportDialog();
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["companyRef"].validate((valid) => {
    if (valid) {
      if (form.value.companyId != null) {
        updateCompany(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addCompany(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _companyIds = row.companyId || ids.value;
  const tipMsg =
    ids.value.length == 0
      ? `是否确认删除"${row.companyName}"?`
      : "是否确认删除所选数据？";
  proxy.$modal
    .confirm(tipMsg)
    .then(function () {
      return delCompany(_companyIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "organization/company/export",
    {
      ...queryParams.value,
    },
    `company_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
