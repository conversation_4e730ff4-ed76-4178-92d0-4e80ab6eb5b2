<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="教师姓名" prop="teacherName">
        <el-input
          v-model="queryParams.teacherName"
          placeholder="请输入教师姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="教师类型" prop="teacherType">
        <el-select
          v-model="queryParams.teacherType"
          placeholder="请选择教师类型"
          style="width: 240px"
          clearable
        >
          <el-option
            v-for="dict in teacher_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属组织" prop="orgName">
        <el-input
          v-model="queryParams.orgName"
          placeholder="请输入所属组织"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="职务" prop="position">
        <el-input
          v-model="queryParams.position"
          placeholder="请输入职务"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="性别" prop="gender">
        <el-select
          v-model="queryParams.gender"
          placeholder="请选择性别"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in user_sex"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['organization:teacher:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['organization:teacher:import']"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['organization:teacher:remove']"
          >批量删除</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      border
      :data="teacherList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="80" align="center" />
      <el-table-column
        label="教师姓名"
        width="120"
        align="center"
        prop="teacherName"
      />
      <el-table-column
        label="教师类型"
        width="120"
        align="center"
        prop="teacherType"
      >
        <template #default="scope">
          <dict-tag :options="teacher_type" :value="scope.row.teacherType" />
        </template>
      </el-table-column>
      <el-table-column label="所属组织名称" align="left" prop="orgName" />
      <el-table-column
        label="职务"
        width="150"
        align="center"
        prop="position"
      />
      <el-table-column label="性别" width="80" align="center" prop="gender">
        <template #default="scope">
          <dict-tag :options="user_sex" :value="scope.row.gender" />
        </template>
      </el-table-column>
      <el-table-column
        label="联系方式"
        width="120"
        align="center"
        prop="contactInfo"
      />
      <el-table-column
        width="180"
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleDetail(scope.row)"
            v-hasPermi="['organization:teacher:edit']"
            >查看</el-button
          >
          <el-button
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['organization:teacher:edit']"
            >编辑</el-button
          >
          <el-button
            link
            type="primary"
            @click="handleDelete(scope.row)"
            v-hasPermi="['organization:teacher:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改教师信息对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="teacherRef" :model="form" :rules="rules" label-width="80px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="教师姓名"
              prop="teacherName"
              label-position="top"
            >
              <el-input
                v-model="form.teacherName"
                placeholder="请输入教师姓名"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="教师类型"
              prop="teacherType"
              label-position="top"
            >
              <el-select
                v-model="form.teacherType"
                placeholder="请选择教师类型"
                @change="selectTeacherType"
              >
                <el-option
                  v-for="dict in teacher_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属组织" prop="orgName" label-position="top">
              <el-select v-model="form.orgName" placeholder="请选择所属组织">
                <el-option
                  v-for="dict in orgOption"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="职务" prop="position" label-position="top">
              <el-input v-model="form.position" placeholder="请输入职务" />
            </el-form-item>
          </el-col>
          <el-col :span="12"
            ><el-form-item label="性别" prop="gender" label-position="top">
              <el-select v-model="form.gender" placeholder="请选择性别">
                <el-option
                  v-for="dict in user_sex"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="联系方式"
              prop="contactInfo"
              label-position="top"
            >
              <el-input
                v-model="form.contactInfo"
                placeholder="请输入联系方式"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 详情 -->
    <el-dialog :title="title" v-model="detail" width="600px" append-to-body>
      <el-descriptions class="margin-top" :column="2" direction="vertical">
        <el-descriptions-item label="教师姓名">{{
          form.teacherName
        }}</el-descriptions-item>
        <el-descriptions-item label="教师类型">{{
          form.teacherType || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="所属组织">{{
          form.orgName || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="职务">{{
          form.position || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="性别">{{
          form.gender || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="联系方式">{{
          form.contactInfo || "-"
        }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detail = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 教师导入对话框 -->
    <ImportDialog
      v-model="importDialogVisible"
      :title="importTitle"
      :import-url="importUrl"
      :template-url="templateUrl"
      @success="handleImportSuccess"
      @error="handleImportError"
      @download-template="handleDownloadTemplate"
    />
  </div>
</template>

<script setup name="Teacher">
import {
  listTeacher,
  getTeacher,
  delTeacher,
  addTeacher,
  updateTeacher,
} from "@/api/organization/teacher";
import { listStructure } from "@/api/organization/structure";
import { listCompany } from "@/api/organization/company";
import ImportDialog from "@/components/ImportDialog/index.vue";
import { useImport, createImportConfig } from "@/composables/useImport";

const { proxy } = getCurrentInstance();
const { user_sex, teacher_type } = proxy.useDict("user_sex", "teacher_type");

const teacherList = ref([]);
const open = ref(false);
const detail = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const orgOption = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    teacherName: null,
    teacherType: null,
    orgName: null,
    position: null,
    gender: null,
  },
  rules: {
    teacherName: [
      { required: true, message: "请输入教师姓名！", trigger: "blur" },
    ],
    teacherType: [
      { required: true, message: "请选择教师类型！", trigger: "change" },
    ],
    orgName: [{ required: true, message: "请输入所属组织！", trigger: "blur" }],
    // contactInfo: [
    //   { required: true, message: "请输入联系方式！", trigger: "blur" },
    //   {
    //     pattern: /^\d{11}$/,
    //     message: "手机号必须为11位数字",
    //     trigger: "blur",
    //   },
    // ],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询教师信息列表 */
function getList() {
  loading.value = true;
  listTeacher(queryParams.value).then((response) => {
    teacherList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    teacherId: null,
    teacherName: null,
    teacherType: null,
    orgName: null,
    position: null,
    gender: null,
    contactInfo: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
  };
  proxy.resetForm("teacherRef");
}
// 使用导入组合式函数
const importConfig = createImportConfig(
  "/organization/teacher",
  "教师",
  getList,
  proxy.download
);

const {
  importDialogVisible,
  importTitle,
  openImportDialog,
  handleImportSuccess,
  handleImportError,
  handleDownloadTemplate,
  importUrl,
  templateUrl,
} = useImport(importConfig);

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.teacherId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加教师信息";
}
/** 修改按钮操作 */
function handleDetail(row) {
  reset();
  const _teacherId = row.teacherId || ids.value;
  getTeacher(_teacherId).then((response) => {
    form.value = response.data;
    detail.value = true;
    title.value = "详情";
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _teacherId = row.teacherId || ids.value;
  getTeacher(_teacherId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改教师信息";
    if (form.value.teacherType == "校内教师") {
      getOrgList("school");
    } else {
      getOrgList("company");
    }
  });
}
/** 导入按钮操作 */
function handleImport() {
  openImportDialog();
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["teacherRef"].validate((valid) => {
    if (valid) {
      if (form.value.teacherId != null) {
        updateTeacher(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addTeacher(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _teacherIds = row.teacherId || ids.value;
  const tipMsg =
    ids.value.length == 0
      ? `是否确认删除"${row.teacherName}"?`
      : "是否确认删除所选数据？";
  proxy.$modal
    .confirm(tipMsg)
    .then(function () {
      return delTeacher(_teacherIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "organization/teacher/export",
    {
      ...queryParams.value,
    },
    `teacher_${new Date().getTime()}.xlsx`
  );
}
/** 教师类型选择 */
function selectTeacherType(item) {
  form.value.orgName='';
  orgOption.value = [];
  if (item == "校内教师") {
    getOrgList("school");
  } else {
    getOrgList("company");
  }
}
/** 查询机构信息下拉 */
function getOrgList(type) {
  if (type == "school") {
    listStructure().then((response) => {
      if (response.data.length > 0) {
        response.data.forEach((item, index) => {
          orgOption.value.push({
            label: item.structureName,
            value: item.structureName,
          });
        });
      }
    });
  }
  if (type == "company") {
    listCompany().then((response) => {
      if (response.rows.length > 0) {
        response.rows.forEach((item, index) => {
          orgOption.value.push({
            label: item.companyName,
            value: item.companyName,
          });
        });
      }
    });
  }
}
getList();
</script>
